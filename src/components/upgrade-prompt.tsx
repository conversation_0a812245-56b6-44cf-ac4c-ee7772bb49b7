'use client'

import { Crown, Zap, TrendingUp, Download, BarChart3, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useState, useEffect } from 'react'

interface UpgradePromptProps {
  variant?: 'banner' | 'modal' | 'inline'
  feature?: string
  onClose?: () => void
  className?: string
}

export function UpgradePrompt({
  variant = 'banner',
  feature = 'analytics',
  onClose,
  className = ''
}: UpgradePromptProps) {
  const [isVisible, setIsVisible] = useState(true)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const handleClose = () => {
    setIsVisible(false)
    onClose?.()
  }

  const handleUpgrade = () => {
    if (mounted) {
      window.open('/pricing', '_blank')
    }
  }

  if (!isVisible) return null

  const features = [
    { icon: TrendingUp, text: 'Real-time analytics data' },
    { icon: BarChart3, text: 'Advanced insights & trends' },
    { icon: Download, text: 'Data export capabilities' },
    { icon: Zap, text: 'Priority support' }
  ]

  if (variant === 'banner') {
    return (
      <div className={`bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-lg ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-2 bg-white/20 rounded-lg">
              <Crown className="w-6 h-6" />
            </div>
            <div>
              <h3 className="font-semibold">Unlock Premium Analytics</h3>
              <p className="text-sm opacity-90">
                Get access to real-time data, advanced insights, and export capabilities
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              onClick={handleUpgrade}
              className="bg-white text-blue-600 hover:bg-gray-100"
              size="sm"
            >
              <Crown className="w-4 h-4 mr-2" />
              Upgrade Now
            </Button>
            {onClose && (
              <button
                onClick={handleClose}
                className="text-white/80 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>
      </div>
    )
  }

  if (variant === 'modal') {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-xl max-w-md w-full p-6">
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Crown className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Upgrade to Premium
            </h2>
            <p className="text-gray-600">
              Unlock the full power of BenefitLens analytics
            </p>
          </div>

          <div className="space-y-3 mb-6">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <div key={index} className="flex items-center space-x-3">
                  <div className="p-1 bg-blue-100 rounded">
                    <Icon className="w-4 h-4 text-blue-600" />
                  </div>
                  <span className="text-sm text-gray-700">{feature.text}</span>
                </div>
              )
            })}
          </div>

          <div className="flex space-x-3">
            <Button
              onClick={handleUpgrade}
              className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              <Crown className="w-4 h-4 mr-2" />
              Upgrade Now
            </Button>
            <Button
              onClick={handleClose}
              variant="outline"
              className="flex-1"
            >
              Maybe Later
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Inline variant
  return (
    <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start space-x-3">
        <div className="p-2 bg-blue-100 rounded-lg">
          <Crown className="w-5 h-5 text-blue-600" />
        </div>
        <div className="flex-1">
          <h4 className="font-medium text-gray-900 mb-1">
            Premium Feature
          </h4>
          <p className="text-sm text-gray-600 mb-3">
            This {feature} feature is available with a Premium subscription. 
            Upgrade to access real-time data and advanced insights.
          </p>
          <Button
            onClick={handleUpgrade}
            size="sm"
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Crown className="w-4 h-4 mr-2" />
            Learn More
          </Button>
        </div>
        {onClose && (
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  )
}

export function FeatureLockedOverlay({
  feature = 'feature',
  className = ''
}: {
  feature?: string
  className?: string
}) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])
  return (
    <div className={`absolute inset-0 bg-white/90 backdrop-blur-sm flex items-center justify-center rounded-lg ${className}`}>
      <div className="text-center p-6">
        <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-3">
          <Crown className="w-6 h-6 text-white" />
        </div>
        <h3 className="font-semibold text-gray-900 mb-2">Premium Feature</h3>
        <p className="text-sm text-gray-600 mb-4">
          Upgrade to access {feature}
        </p>
        <Button
          onClick={() => {
            if (mounted) {
              window.open('/pricing', '_blank')
            }
          }}
          size="sm"
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Crown className="w-4 h-4 mr-2" />
          Upgrade Now
        </Button>
      </div>
    </div>
  )
}
