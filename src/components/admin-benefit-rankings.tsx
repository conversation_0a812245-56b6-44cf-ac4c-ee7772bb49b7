'use client'

import { useState, useEffect } from 'react'
import {
  Tren<PERSON>Up,
  <PERSON><PERSON><PERSON><PERSON>,
  Trash2,
  <PERSON><PERSON>reshC<PERSON>,
  Eye,
  Search,
  Info,
  Users,
  Star,
  Award
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface BenefitRankingStats {
  benefit_id: string
  benefit_name: string
  category: string
  icon: string | null
  total_rankings: number
  average_ranking: number
  best_ranking: number
  worst_ranking: number
  top_3_count: number
  bottom_3_count: number
}

interface RankingUser {
  id: string
  email: string
  first_name: string | null
  last_name: string | null
  ranking_count: number
  last_ranking_update: string
  // Additional properties when fetching individual benefit rankings
  ranking?: number
  user_email?: string
  updated_at?: string
}

export function AdminBenefitRankings() {
  const [stats, setStats] = useState<{
    benefitStats: BenefitRankingStats[]
    activeUsers: RankingUser[]
    totalBenefitsRanked: number
    totalRankings: number
  } | null>(null)
  const [loading, setLoading] = useState(true)
  const [resetting, setResetting] = useState<string | null>(null)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedBenefit, setSelectedBenefit] = useState<string | null>(null)
  const [benefitRankings, setBenefitRankings] = useState<RankingUser[]>([])
  const [viewingBenefit, setViewingBenefit] = useState(false)

  useEffect(() => {
    fetchRankingStats()
  }, [])

  const fetchRankingStats = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/benefit-rankings')
      
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      } else {
        setMessage({ type: 'error', text: 'Failed to fetch ranking statistics' })
      }
    } catch (error) {
      console.error('Error fetching ranking stats:', error)
      setMessage({ type: 'error', text: 'Failed to fetch ranking statistics' })
    } finally {
      setLoading(false)
    }
  }

  const fetchBenefitRankings = async (benefitId: string) => {
    try {
      setViewingBenefit(true)
      const response = await fetch(`/api/admin/benefit-rankings?benefitId=${benefitId}&includeUserDetails=true`)
      
      if (response.ok) {
        const data = await response.json()
        setBenefitRankings(data.rankings || [])
        setSelectedBenefit(benefitId)
      } else {
        setMessage({ type: 'error', text: 'Failed to fetch benefit rankings' })
      }
    } catch (error) {
      console.error('Error fetching benefit rankings:', error)
      setMessage({ type: 'error', text: 'Failed to fetch benefit rankings' })
    } finally {
      setViewingBenefit(false)
    }
  }

  const resetBenefitRankings = async (benefitId: string, benefitName: string) => {
    if (!confirm(`Are you sure you want to reset all rankings for "${benefitName}"? This action cannot be undone.`)) {
      return
    }

    try {
      setResetting(benefitId)
      setMessage(null)

      const response = await fetch(`/api/admin/benefit-rankings?benefitId=${benefitId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        const data = await response.json()
        setMessage({ type: 'success', text: data.message })
        await fetchRankingStats()
        
        // Clear selected benefit if it was reset
        if (selectedBenefit === benefitId) {
          setSelectedBenefit(null)
          setBenefitRankings([])
        }
      } else {
        const error = await response.json()
        setMessage({ type: 'error', text: error.error || 'Failed to reset rankings' })
      }
    } catch (error) {
      console.error('Error resetting rankings:', error)
      setMessage({ type: 'error', text: 'Failed to reset rankings' })
    } finally {
      setResetting(null)
    }
  }

  const getRankingColor = (ranking: number) => {
    if (ranking <= 3) return 'text-green-600 bg-green-50 border-green-200'
    if (ranking <= 6) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  const filteredBenefits = stats?.benefitStats.filter(benefit =>
    benefit.benefit_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    benefit.category.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-700">Loading ranking statistics...</p>
        </div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="text-center py-8">
        <BarChart3 className="w-12 h-12 mx-auto mb-4 text-gray-500" />
        <p className="text-gray-600">No ranking data available</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {message && (
        <Alert className={`${message.type === 'error' ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}`}>
          <Info className="w-4 h-4" />
          <AlertDescription className={message.type === 'error' ? 'text-red-800' : 'text-green-800'}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Rankings</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.totalRankings.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Star className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Benefits Ranked</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.totalBenefitsRanked}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <TrendingUp className="w-6 h-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Users</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.activeUsers.length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <BarChart3 className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg Rankings/User</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.activeUsers.length > 0
                  ? Math.round(stats.totalRankings / stats.activeUsers.length * 10) / 10
                  : 0
                }
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Benefit Rankings */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Award className="w-5 h-5 mr-2 text-yellow-600" />
                Benefit Rankings
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Manage rankings for all benefits
              </p>
            </div>
            <Button
              onClick={fetchRankingStats}
              variant="outline"
              size="sm"
              disabled={loading}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </div>
            <div className="mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search benefits..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white w-full"
                />
              </div>
            </div>

            <div className="space-y-3 max-h-96 overflow-y-auto">
              {filteredBenefits.map((benefit) => (
                <div key={benefit.benefit_id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      {benefit.icon && <span>{benefit.icon}</span>}
                      <span className="font-medium text-gray-900">{benefit.benefit_name}</span>
                    </div>
                    <Badge variant="outline" className="text-xs text-gray-900">
                      {benefit.category}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <div className="text-right text-sm">
                      <div className="font-medium text-gray-900">
                        {benefit.total_rankings} rankings
                      </div>
                      <div className="text-gray-500">
                        Avg: {Number(benefit.average_ranking).toFixed(2)}
                      </div>
                    </div>
                    
                    <Button
                      onClick={() => fetchBenefitRankings(benefit.benefit_id)}
                      variant="outline"
                      size="sm"
                      disabled={viewingBenefit}
                    >
                      <Eye className="w-3 h-3" />
                    </Button>
                    
                    <Button
                      onClick={() => resetBenefitRankings(benefit.benefit_id, benefit.benefit_name)}
                      variant="outline"
                      size="sm"
                      disabled={resetting === benefit.benefit_id}
                      className="text-red-600 border-red-300 hover:bg-red-50 hover:text-red-700"
                    >
                      {resetting === benefit.benefit_id ? (
                        <RefreshCw className="w-3 h-3 animate-spin" />
                      ) : (
                        <Trash2 className="w-3 h-3" />
                      )}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
        </div>

        {/* Active Users */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Users className="w-5 h-5 mr-2 text-blue-600" />
                Most Active Users
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Users with the most benefit rankings
              </p>
            </div>
          </div>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {stats.activeUsers.map((user, index) => (
                <div key={user.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Badge className="bg-white text-gray-700 border">
                      #{index + 1}
                    </Badge>
                    <div>
                      <div className="font-medium text-gray-900">
                        {user.first_name && user.last_name 
                          ? `${user.first_name} ${user.last_name}`
                          : user.email
                        }
                      </div>
                      <div className="text-sm text-gray-500">
                        {user.email}
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="font-medium text-gray-900">
                      {user.ranking_count} rankings
                    </div>
                    <div className="text-sm text-gray-500">
                      Last: {new Date(user.last_ranking_update).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
        </div>
      </div>

      {/* Selected Benefit Rankings */}
      {selectedBenefit && benefitRankings.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Eye className="w-5 h-5 mr-2 text-blue-600" />
                Individual Rankings
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                All user rankings for {stats?.benefitStats.find(b => b.benefit_id === selectedBenefit)?.benefit_name || 'Unknown Benefit'}
              </p>
            </div>
            <Button
              onClick={() => {
                setSelectedBenefit(null)
                setBenefitRankings([])
              }}
              variant="outline"
              size="sm"
            >
              Close
            </Button>
          </div>
            <div className="space-y-2">
              {benefitRankings.map((ranking) => (
                <div key={ranking.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Badge className={getRankingColor(ranking.ranking || 0)}>
                      #{ranking.ranking || 0}
                    </Badge>
                    <div>
                      <div className="font-medium text-gray-900">
                        {ranking.first_name && ranking.last_name 
                          ? `${ranking.first_name} ${ranking.last_name}`
                          : ranking.user_email
                        }
                      </div>
                      <div className="text-sm text-gray-500">
                        {ranking.user_email}
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-sm text-gray-500">
                    {ranking.updated_at ? new Date(ranking.updated_at).toLocaleDateString() : 'N/A'}
                  </div>
                </div>
              ))}
            </div>
        </div>
      )}
    </div>
  )
}
