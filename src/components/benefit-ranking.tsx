'use client'

import { useState, useEffect, useRef } from 'react'
import dynamic from 'next/dynamic'

// Dynamically import drag and drop components to avoid SSR issues
const DragDropContext = dynamic(
  () => import('@hello-pangea/dnd').then(mod => mod.DragDropContext),
  { ssr: false }
)
const Droppable = dynamic(
  () => import('@hello-pangea/dnd').then(mod => mod.Droppable),
  { ssr: false }
)
const Draggable = dynamic(
  () => import('@hello-pangea/dnd').then(mod => mod.Draggable),
  { ssr: false }
)

import type { DropResult } from '@hello-pangea/dnd'
import {
  Star,
  GripVertical,
  RotateCcw,
  Info,
  TrendingUp,
  Award,
  ArrowRight,
  ArrowLeft,
  ChevronUp,
  ChevronDown
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import type { Benefit, UserBenefitRanking } from '@/types/database'

interface RankedBenefit extends Benefit {
  ranking?: number
  isRanked: boolean
}

interface BenefitRankingProps {
  className?: string
}

export function BenefitRanking({ className }: BenefitRankingProps) {
  const [_benefits, _setBenefits] = useState<RankedBenefit[]>([])
  const [rankedBenefits, setRankedBenefits] = useState<RankedBenefit[]>([])
  const [unrankedBenefits, setUnrankedBenefits] = useState<RankedBenefit[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)
  const [autoSaving, setAutoSaving] = useState(false)
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const isAutoSavingRef = useRef(false)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    fetchBenefitsAndRankings()

    // Cleanup function to clear timeouts
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current)
      }
    }
  }, [])

  // Auto-save when rankedBenefits changes (but not on initial load)
  useEffect(() => {
    if (mounted && rankedBenefits.length > 0) {
      debouncedAutoSave()
    }
  }, [rankedBenefits, mounted])

  const fetchBenefitsAndRankings = async () => {
    try {
      setLoading(true)

      // Fetch all benefits with proper IDs
      const benefitsResponse = await fetch('/api/benefits')
      const benefitsData = await benefitsResponse.json()

      // Fetch user's current rankings
      const rankingsResponse = await fetch('/api/user/benefit-rankings?includeDetails=true')
      let userRankings: UserBenefitRanking[] = []

      if (rankingsResponse.ok) {
        const rankingsData = await rankingsResponse.json()
        userRankings = rankingsData.rankings || []
      }

      // Create a map of benefit rankings by benefit ID
      const _rankingMap = new Map(userRankings.map(r => [r.benefit_id, r.ranking]))

      // Create a map of benefit rankings by benefit ID for quick lookup

      // Process benefits and separate ranked/unranked
      const processedBenefits: RankedBenefit[] = benefitsData.map((benefit: { id: string; name: string; category_id: string; category_name: string; icon?: string; description?: string; created_at: string }) => {
        const userRanking = userRankings.find(r => r.benefit_id === benefit.id)

        return {
          id: benefit.id, // Use the actual UUID from the database
          name: benefit.name,
          category_id: benefit.category_id,
          category_name: benefit.category_name,
          icon: benefit.icon,
          description: benefit.description,
          created_at: benefit.created_at,
          ranking: userRanking?.ranking,
          isRanked: !!userRanking
        }
      })

      const ranked = processedBenefits
        .filter(b => b.isRanked)
        .sort((a, b) => (a.ranking || 0) - (b.ranking || 0))

      const unranked = processedBenefits.filter(b => !b.isRanked)

      _setBenefits(processedBenefits)
      setRankedBenefits(ranked)
      setUnrankedBenefits(unranked)

    } catch (error) {
      console.error('Error fetching benefits and rankings:', error)
      setMessage({ type: 'error', text: 'Failed to load benefits and rankings' })
    } finally {
      setLoading(false)
    }
  }

  const handleDragEnd = (result: DropResult) => {
    const { destination, source, draggableId } = result

    if (!destination) return

    // Handle moving between lists
    if (source.droppableId !== destination.droppableId) {
      if (source.droppableId === 'unranked' && destination.droppableId === 'ranked') {
        // Moving from unranked to ranked
        const benefit = unrankedBenefits.find(b => b.id === draggableId)
        if (!benefit) return

        const newRankedBenefits = [...rankedBenefits]
        const rankedBenefit = { ...benefit, ranking: destination.index + 1, isRanked: true }
        newRankedBenefits.splice(destination.index, 0, rankedBenefit)
        
        // Update rankings for all benefits after the insertion point
        for (let i = destination.index + 1; i < newRankedBenefits.length; i++) {
          newRankedBenefits[i].ranking = i + 1
        }

        setRankedBenefits(newRankedBenefits)
        setUnrankedBenefits(unrankedBenefits.filter(b => b.id !== draggableId))
      } else if (source.droppableId === 'ranked' && destination.droppableId === 'unranked') {
        // Moving from ranked to unranked
        const benefit = rankedBenefits.find(b => b.id === draggableId)
        if (!benefit) return

        const newRankedBenefits = rankedBenefits.filter(b => b.id !== draggableId)
        // Update rankings for remaining benefits
        newRankedBenefits.forEach((b, index) => {
          b.ranking = index + 1
        })

        const unrankedBenefit = { ...benefit, ranking: undefined, isRanked: false }
        const newUnrankedBenefits = [...unrankedBenefits]
        newUnrankedBenefits.splice(destination.index, 0, unrankedBenefit)

        setRankedBenefits(newRankedBenefits)
        setUnrankedBenefits(newUnrankedBenefits)
      }
    } else if (source.droppableId === 'ranked' && destination.droppableId === 'ranked') {
      // Reordering within ranked list
      const newRankedBenefits = [...rankedBenefits]
      const [removed] = newRankedBenefits.splice(source.index, 1)
      newRankedBenefits.splice(destination.index, 0, removed)
      
      // Update all rankings
      newRankedBenefits.forEach((benefit, index) => {
        benefit.ranking = index + 1
      })
      
      setRankedBenefits(newRankedBenefits)
    } else if (source.droppableId === 'unranked' && destination.droppableId === 'unranked') {
      // Reordering within unranked list
      const newUnrankedBenefits = [...unrankedBenefits]
      const [removed] = newUnrankedBenefits.splice(source.index, 1)
      newUnrankedBenefits.splice(destination.index, 0, removed)
      setUnrankedBenefits(newUnrankedBenefits)
    }
  }

  const debouncedAutoSave = () => {
    // Clear any existing timeout
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current)
    }

    // Set a new timeout for auto-save
    autoSaveTimeoutRef.current = setTimeout(() => {
      autoSaveRankings()
    }, 1000) // Wait 1 second after last change
  }

  const autoSaveRankings = async () => {
    // Prevent multiple simultaneous auto-saves
    if (isAutoSavingRef.current) {
      return
    }

    try {
      isAutoSavingRef.current = true
      setAutoSaving(true)
      setMessage(null)

      // Validate rankings before saving
      const currentRankings = rankedBenefits.map((benefit, index) => ({
        benefit_id: benefit.id,
        ranking: index + 1 // Ensure sequential rankings
      }))

      // Double-check for duplicates
      const rankingValues = currentRankings.map(r => r.ranking)
      const uniqueRankings = new Set(rankingValues)
      if (rankingValues.length !== uniqueRankings.size) {
        console.error('Duplicate rankings detected, skipping auto-save')
        setMessage({ type: 'error', text: 'Ranking conflict detected. Please refresh the page.' })
        return
      }

      // Create an AbortController for timeout
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

      const response = await fetch('/api/user/benefit-rankings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ rankings: currentRankings }),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (response.ok) {
        // Clear any existing error messages on successful save
        setMessage(null)
      } else {
        const error = await response.json()
        setMessage({ type: 'error', text: error.error || 'Failed to save rankings' })
      }
    } catch (error) {
      console.error('Error auto-saving rankings:', error)
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          setMessage({ type: 'error', text: 'Save request timed out. Please try again.' })
        } else if (error.message.includes('Failed to fetch')) {
          setMessage({ type: 'error', text: 'Network error. Please check your connection.' })
        } else {
          setMessage({ type: 'error', text: 'Failed to save rankings' })
        }
      } else {
        setMessage({ type: 'error', text: 'Failed to save rankings' })
      }
    } finally {
      setAutoSaving(false)
      isAutoSavingRef.current = false
    }
  }

  const _saveRankings = async () => {
    try {
      setSaving(true)
      setMessage(null)

      const rankingsToSave = rankedBenefits.map(benefit => ({
        benefit_id: benefit.id,
        ranking: benefit.ranking!
      }))

      const response = await fetch('/api/user/benefit-rankings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ rankings: rankingsToSave })
      })

      if (response.ok) {
        setMessage({ type: 'success', text: 'Rankings saved successfully!' })
        // Refresh data to ensure consistency
        await fetchBenefitsAndRankings()
      } else {
        const error = await response.json()
        setMessage({ type: 'error', text: error.error || 'Failed to save rankings' })
      }
    } catch (error) {
      console.error('Error saving rankings:', error)
      setMessage({ type: 'error', text: 'Failed to save rankings' })
    } finally {
      setSaving(false)
    }
  }

  const resetRankings = async () => {
    if (!confirm('Are you sure you want to reset all your benefit rankings?')) {
      return
    }

    try {
      setSaving(true)
      setMessage(null)

      const response = await fetch('/api/user/benefit-rankings', {
        method: 'DELETE'
      })

      if (response.ok) {
        setMessage({ type: 'success', text: 'Rankings reset successfully!' })
        await fetchBenefitsAndRankings()
      } else {
        const error = await response.json()
        setMessage({ type: 'error', text: error.error || 'Failed to reset rankings' })
      }
    } catch (error) {
      console.error('Error resetting rankings:', error)
      setMessage({ type: 'error', text: 'Failed to reset rankings' })
    } finally {
      setSaving(false)
    }
  }

  const getRankingColor = (ranking: number) => {
    if (ranking <= 3) return 'text-green-600 bg-green-50'
    if (ranking <= 6) return 'text-yellow-600 bg-yellow-50'
    return 'text-red-600 bg-red-50'
  }

  const getRankingIcon = (ranking: number) => {
    if (ranking === 1) return <Award className="w-4 h-4" />
    if (ranking <= 3) return <TrendingUp className="w-4 h-4" />
    return <Star className="w-4 h-4" />
  }

  const addToRanked = (benefit: RankedBenefit) => {
    if (rankedBenefits.length >= 10) {
      setMessage({ type: 'error', text: 'You can only rank up to 10 benefits' })
      return
    }

    const newRanking = rankedBenefits.length + 1
    const rankedBenefit = { ...benefit, ranking: newRanking, isRanked: true }

    setRankedBenefits([...rankedBenefits, rankedBenefit])
    setUnrankedBenefits(unrankedBenefits.filter(b => b.id !== benefit.id))
  }

  const removeFromRanked = (benefit: RankedBenefit) => {
    const newRankedBenefits = rankedBenefits.filter(b => b.id !== benefit.id)
    // Update rankings for remaining benefits
    newRankedBenefits.forEach((b, index) => {
      b.ranking = index + 1
    })

    const unrankedBenefit = { ...benefit, ranking: undefined, isRanked: false }

    setRankedBenefits(newRankedBenefits)
    setUnrankedBenefits([...unrankedBenefits, unrankedBenefit])
  }

  const moveUp = (index: number) => {
    if (index === 0) return

    const newRankedBenefits = [...rankedBenefits]
    const temp = newRankedBenefits[index]
    newRankedBenefits[index] = newRankedBenefits[index - 1]
    newRankedBenefits[index - 1] = temp

    // Update rankings
    newRankedBenefits.forEach((benefit, idx) => {
      benefit.ranking = idx + 1
    })

    setRankedBenefits(newRankedBenefits)
  }

  const moveDown = (index: number) => {
    if (index === rankedBenefits.length - 1) return

    const newRankedBenefits = [...rankedBenefits]
    const temp = newRankedBenefits[index]
    newRankedBenefits[index] = newRankedBenefits[index + 1]
    newRankedBenefits[index + 1] = temp

    // Update rankings
    newRankedBenefits.forEach((benefit, idx) => {
      benefit.ranking = idx + 1
    })

    setRankedBenefits(newRankedBenefits)
  }

  if (loading || !mounted) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading benefits...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      {message && message.type === 'error' && (
        <div className="mb-6">
          <Alert className="border-red-200 bg-red-50">
            <Info className="w-4 h-4" />
            <AlertDescription className="text-red-800">
              {message.text}
            </AlertDescription>
          </Alert>
        </div>
      )}

      {mounted && DragDropContext ? (
        <DragDropContext onDragEnd={handleDragEnd}>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Ranked Benefits */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-gray-900">
                <div className="flex items-center">
                  <Award className="w-5 h-5 mr-2 text-yellow-600" />
                  Ranked Benefits ({rankedBenefits.length}/10)
                </div>
                {rankedBenefits.length > 0 && (
                  <Button
                    onClick={resetRankings}
                    variant="destructive"
                    size="sm"
                    disabled={saving || autoSaving}
                  >
                    <RotateCcw className="w-4 h-4 mr-2" />
                    Reset
                  </Button>
                )}
              </CardTitle>
              <CardDescription>
                Your top benefits in order of importance
                {autoSaving && (
                  <span className="text-green-600 ml-2">• Saving...</span>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Droppable droppableId="ranked">
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className={`min-h-[300px] space-y-3 p-4 rounded-lg border-2 border-dashed transition-all duration-200 ${
                      snapshot.isDraggingOver
                        ? 'border-green-400 bg-green-50 shadow-lg'
                        : rankedBenefits.length === 0
                        ? 'border-gray-300 bg-gray-50'
                        : 'border-gray-200 bg-white'
                    }`}
                  >
                    {rankedBenefits.map((benefit, index) => (
                      <Draggable key={benefit.id} draggableId={benefit.id} index={index}>
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className={`flex items-center space-x-3 p-4 bg-white border rounded-lg shadow-sm transition-shadow ${
                              snapshot.isDragging ? 'shadow-lg' : 'hover:shadow-md'
                            }`}
                          >
                            <div
                              {...provided.dragHandleProps}
                              className="text-gray-500 hover:text-gray-700 cursor-grab active:cursor-grabbing"
                            >
                              <GripVertical className="w-4 h-4" />
                            </div>

                            <Badge className={`${getRankingColor(benefit.ranking || 0)} flex items-center space-x-1 font-semibold`}>
                              {getRankingIcon(benefit.ranking || 0)}
                              <span>#{benefit.ranking}</span>
                            </Badge>

                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2">
                                {benefit.icon && <span className="text-lg">{benefit.icon}</span>}
                                <span className="font-semibold text-gray-900 truncate">{benefit.name}</span>
                              </div>
                              <div className="text-sm text-gray-600 capitalize">
                                {benefit.category_name?.replace('_', ' ') || 'Other'}
                              </div>
                            </div>

                            <div className="flex items-center space-x-1">
                              <button
                                onClick={() => moveUp(index)}
                                disabled={index === 0}
                                className="p-1 text-gray-500 hover:text-gray-700 disabled:opacity-30 disabled:cursor-not-allowed"
                                title="Move up"
                              >
                                <ChevronUp className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => moveDown(index)}
                                disabled={index === rankedBenefits.length - 1}
                                className="p-1 text-gray-500 hover:text-gray-700 disabled:opacity-30 disabled:cursor-not-allowed"
                                title="Move down"
                              >
                                <ChevronDown className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => removeFromRanked(benefit)}
                                className="p-1 text-red-400 hover:text-red-600"
                                title="Remove from ranking"
                              >
                                <ArrowLeft className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                    
                    {rankedBenefits.length === 0 && (
                      <div className="text-center py-12 text-gray-500">
                        <Award className="w-12 h-12 mx-auto mb-4 opacity-30" />
                        <p className="text-lg font-medium mb-2">No benefits ranked yet</p>
                        <p className="text-sm">Drag benefits here or use the → button to start ranking</p>
                      </div>
                    )}
                  </div>
                )}
              </Droppable>
            </CardContent>
          </Card>

          {/* Unranked Benefits */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-gray-900">
                <Star className="w-5 h-5 mr-2 text-gray-500" />
                Available Benefits ({unrankedBenefits.length})
              </CardTitle>
              <CardDescription>
                Drag benefits to the ranked list to prioritize them
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Droppable droppableId="unranked">
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className={`min-h-[300px] space-y-3 p-4 rounded-lg border-2 border-dashed transition-all duration-200 ${
                      snapshot.isDraggingOver
                        ? 'border-blue-400 bg-blue-50 shadow-lg'
                        : 'border-gray-200 bg-gray-50'
                    }`}
                  >
                    {unrankedBenefits.map((benefit, index) => (
                      <Draggable key={benefit.id} draggableId={benefit.id} index={index}>
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className={`flex items-center space-x-3 p-4 bg-white border rounded-lg shadow-sm transition-shadow ${
                              snapshot.isDragging ? 'shadow-lg' : 'hover:shadow-md'
                            }`}
                          >
                            <div
                              {...provided.dragHandleProps}
                              className="text-gray-500 hover:text-gray-700 cursor-grab active:cursor-grabbing"
                            >
                              <GripVertical className="w-4 h-4" />
                            </div>

                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2">
                                {benefit.icon && <span className="text-lg">{benefit.icon}</span>}
                                <span className="font-semibold text-gray-900 truncate">{benefit.name}</span>
                              </div>
                              <div className="text-sm text-gray-600 capitalize">
                                {benefit.category_name?.replace('_', ' ') || 'Other'}
                              </div>
                            </div>

                            <button
                              onClick={() => addToRanked(benefit)}
                              disabled={rankedBenefits.length >= 10}
                              className="p-2 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded-md disabled:opacity-30 disabled:cursor-not-allowed transition-colors"
                              title="Add to ranking"
                            >
                              <ArrowRight className="w-4 h-4" />
                            </button>
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </CardContent>
          </Card>
          </div>
        </DragDropContext>
      ) : (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading drag and drop interface...</p>
        </div>
      )}
    </div>
  )
}
