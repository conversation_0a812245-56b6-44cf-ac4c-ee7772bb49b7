'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON><PERSON><PERSON>gle, BarChart3, Trash2, Refresh<PERSON><PERSON> } from 'lucide-react'

interface AnalyticsResetInfo {
  currentCounts: {
    company_page_views: number
    search_queries: number
    benefit_interactions: number
    daily_summaries: number
    company_summaries: number
  }
  companiesWithData: Array<{
    id: string
    name: string
    page_views: number
    interactions: number
    summary_records: number
  }>
  resetOptions: Array<{
    value: string
    label: string
    description: string
  }>
}

interface ResetResult {
  success: boolean
  message: string
  resetCount: number
  resetDetails: string[]
  resetType: string
  companyId?: string
  timestamp: string
}

export function AdminAnalyticsReset() {
  const [resetInfo, setResetInfo] = useState<AnalyticsResetInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [resetting, setResetting] = useState(false)
  const [lastResetResult, setLastResetResult] = useState<ResetResult | null>(null)

  useEffect(() => {
    fetchResetInfo()
  }, [])

  const fetchResetInfo = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/analytics/reset')
      if (response.ok) {
        const data = await response.json()
        setResetInfo(data)
      } else {
        console.error('Failed to fetch reset info')
      }
    } catch (error) {
      console.error('Error fetching reset info:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleReset = async (resetType: string, companyId?: string) => {
    const confirmMessage = companyId 
      ? `Are you sure you want to reset ${resetType} analytics data for the selected company? This action cannot be undone.`
      : `Are you sure you want to reset ${resetType} analytics data? This action cannot be undone.`
    
    if (!confirm(confirmMessage)) {
      return
    }

    try {
      setResetting(true)
      const url = new URL('/api/admin/analytics/reset', window.location.origin)
      url.searchParams.set('type', resetType)
      if (companyId) {
        url.searchParams.set('companyId', companyId)
      }

      const response = await fetch(url.toString(), {
        method: 'POST'
      })

      if (response.ok) {
        const result = await response.json()
        setLastResetResult(result)
        // Refresh the data counts
        await fetchResetInfo()
      } else {
        const error = await response.json()
        alert(`Reset failed: ${error.error}`)
      }
    } catch (error) {
      console.error('Error resetting analytics:', error)
      alert('Reset failed: Network error')
    } finally {
      setResetting(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        Loading analytics reset information...
      </div>
    )
  }

  if (!resetInfo) {
    return (
      <div className="text-center p-8 text-red-600">
        Failed to load analytics reset information
      </div>
    )
  }

  const totalRecords = Object.values(resetInfo.currentCounts).reduce((sum, count) => sum + count, 0)

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Analytics Data Reset</h3>
          <p className="text-sm text-gray-600 mt-1">
            Manage and reset analytics data across the system. Use with caution.
          </p>
        </div>
      </div>

      <div className="space-y-6">

      {/* Last Reset Result */}
      {lastResetResult && (
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="text-green-800 flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Reset Completed Successfully
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-green-700 mb-2">{lastResetResult.message}</p>
            <div className="text-sm text-green-600">
              <p>Total records reset: <strong>{lastResetResult.resetCount}</strong></p>
              <p>Reset type: <strong>{lastResetResult.resetType}</strong></p>
              <p>Timestamp: <strong>{new Date(lastResetResult.timestamp).toLocaleString()}</strong></p>
              {lastResetResult.resetDetails.length > 0 && (
                <div className="mt-2">
                  <p className="font-medium">Details:</p>
                  <ul className="list-disc list-inside ml-2">
                    {lastResetResult.resetDetails.map((detail, index) => (
                      <li key={index}>{detail}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Current Data Counts */}
      <Card>
        <CardHeader>
          <CardTitle className="text-gray-900">Current Analytics Data</CardTitle>
          <CardDescription>
            Overview of analytics data currently stored in the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {resetInfo.currentCounts.company_page_views.toLocaleString()}
              </div>
              <div className="text-sm text-blue-800">Company Page Views</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {resetInfo.currentCounts.search_queries.toLocaleString()}
              </div>
              <div className="text-sm text-green-800">Search Queries</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {resetInfo.currentCounts.benefit_interactions.toLocaleString()}
              </div>
              <div className="text-sm text-purple-800">Benefit Interactions</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {resetInfo.currentCounts.daily_summaries.toLocaleString()}
              </div>
              <div className="text-sm text-orange-800">Daily Summaries</div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">
                {resetInfo.currentCounts.company_summaries.toLocaleString()}
              </div>
              <div className="text-sm text-red-800">Company Summaries</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-600">
                {totalRecords.toLocaleString()}
              </div>
              <div className="text-sm text-gray-800">Total Records</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Global Reset Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-gray-900">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Global Analytics Reset
          </CardTitle>
          <CardDescription>
            Reset analytics data across the entire system. Use with caution.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            {resetInfo.resetOptions.map((option) => (
              <div key={option.value} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">{option.label}</h4>
                  <p className="text-sm text-gray-600">{option.description}</p>
                </div>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleReset(option.value)}
                  disabled={resetting}
                  className="flex items-center gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Reset
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Company-Specific Reset */}
      {resetInfo.companiesWithData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-gray-900">Company-Specific Reset</CardTitle>
            <CardDescription>
              Reset analytics data for specific companies only
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {resetInfo.companiesWithData.map((company) => (
                <div key={company.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{company.name}</h4>
                    <div className="flex gap-4 mt-1">
                      <Badge variant="secondary">{company.page_views} views</Badge>
                      <Badge variant="secondary">{company.interactions} interactions</Badge>
                      <Badge variant="secondary">{company.summary_records} summaries</Badge>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleReset('company_views', company.id)}
                      disabled={resetting}
                    >
                      Reset Views
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleReset('all', company.id)}
                      disabled={resetting}
                    >
                      Reset All
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
      </div>
    </div>
  )
}
