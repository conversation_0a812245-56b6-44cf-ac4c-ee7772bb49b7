'use client'

import { useState, useEffect } from 'react'
import { CheckCircle } from 'lucide-react'
import { BenefitVerification } from '@/components/benefit-verification'
import { CompanyVerificationNotice } from '@/components/company-verification-notice'
import { BenefitVerificationCounts } from '@/components/benefit-verification-counts'
import { useCompanyAuthorization } from '@/hooks/use-company-authorization'

interface CompanyBenefit {
  id: string
  benefit_id: string
  name: string
  category: string
  category_name?: string
  category_display_name?: string
  icon?: string
  is_verified: boolean
  is_admin_verified?: boolean
  added_by?: string
  created_at: string
}

interface BenefitManagementProps {
  companyId: string
  companyName: string
}

export function BenefitManagement({ companyId, companyName }: BenefitManagementProps) {
  const [companyBenefits, setCompanyBenefits] = useState<CompanyBenefit[]>([])
  const [loading, setLoading] = useState(false)

  // Use company-level authorization
  const { authStatus, isLoading: isLoadingAuth } = useCompanyAuthorization(companyId)

  useEffect(() => {
    fetchCompanyBenefits()
  }, [companyId])

  const fetchCompanyBenefits = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/companies/${companyId}/benefits`)
      if (response.ok) {
        const data = await response.json()
        setCompanyBenefits(data)
      }
    } catch (error) {
      console.error('Error fetching company benefits:', error)
    } finally {
      setLoading(false)
    }
  }

  // Group benefits by category
  const categorizedBenefits = companyBenefits.reduce((acc, benefit) => {
    const category = benefit.category_name || 'other'
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(benefit)
    return acc
  }, {} as Record<string, CompanyBenefit[]>)

  const [categories, setCategories] = useState([
    { key: 'health', label: 'Health & Medical' },
    { key: 'time_off', label: 'Time Off' },
    { key: 'financial', label: 'Financial' },
    { key: 'development', label: 'Development' },
    { key: 'wellness', label: 'Wellness' },
    { key: 'work_life', label: 'Work-Life Balance' },
    { key: 'other', label: 'Other' },
  ])

  useEffect(() => {
    fetchCategories()
  }, [])

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/benefit-categories')
      if (response.ok) {
        const data = await response.json()
        setCategories(data.map((cat: any) => ({
          key: cat.name,
          label: cat.display_name,
          icon: cat.icon
        })))
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
      // Keep default categories as fallback
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Company Benefits</h2>
          <p className="text-sm text-gray-600 mt-1">
            View and verify benefits offered by {companyName}. You can confirm or dispute benefits based on your experience.
          </p>
        </div>
      </div>

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 mt-2">Loading benefits...</p>
        </div>
      ) : companyBenefits.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No benefits have been added for this company yet.</p>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Company-level verification notice */}
          {!isLoadingAuth && authStatus && !authStatus.authorized && (
            <div className="mt-8">
              <CompanyVerificationNotice authStatus={authStatus} />
            </div>
          )}

          {categories.map((category) => {
            const categoryBenefits = categorizedBenefits[category.key] || []
            if (categoryBenefits.length === 0) return null

            return (
              <div key={category.key} className="bg-white rounded-lg shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {category.label}
                </h3>
                <div className="space-y-4">
                  {categoryBenefits.map((benefit) => (
                    <div key={benefit.id} className="space-y-3">
                      <div
                        className={`p-4 rounded-lg border ${
                          benefit.is_verified
                            ? 'bg-green-50 border-green-200'
                            : 'bg-gray-50 border-gray-200'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            {benefit.icon && (
                              <span className="text-2xl">{benefit.icon}</span>
                            )}
                            <div>
                              <h4 className="font-medium text-gray-900">
                                {benefit.name}
                              </h4>
                              <p className="text-sm text-gray-500 capitalize">
                                {benefit.category.replace('_', ' ')}
                              </p>
                            </div>
                          </div>
                          {benefit.is_verified ? (
                            <div className="flex items-center space-x-1">
                              <CheckCircle className="w-4 h-4 text-green-600" />
                              <span className="text-xs text-green-700 font-medium">Verified</span>
                            </div>
                          ) : (
                            <div className="flex items-center space-x-1">
                              <div className="w-4 h-4 rounded-full bg-yellow-400 flex items-center justify-center">
                                <div className="w-2 h-2 rounded-full bg-white"></div>
                              </div>
                              <span className="text-xs text-yellow-700 font-medium">Pending</span>
                            </div>
                          )}
                        </div>

                        {/* Verification counts for all users */}
                        <BenefitVerificationCounts companyBenefitId={benefit.id} />
                      </div>

                      {/* Benefit Verification Component - Only for authorized users */}
                      {!isLoadingAuth && authStatus && authStatus.authorized && (
                        <BenefitVerification
                          companyBenefitId={benefit.id}
                          benefitName={benefit.name}
                          companyName={companyName}
                          benefitId={benefit.benefit_id}
                          companyId={companyId}
                          onVerificationComplete={fetchCompanyBenefits}
                          companyAuthStatus={authStatus}
                          hideAuthRestriction={true}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}


