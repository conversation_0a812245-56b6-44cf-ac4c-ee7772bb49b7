// import { LocationData } from './location-normalization'

export interface GeocodingResult {
  formatted_address: string
  city: string
  country: string
  country_code: string
  latitude: number
  longitude: number
  confidence: number
}

export interface GeocodingProvider {
  name: string
  geocode(location: string): Promise<GeocodingResult | null>
  autocomplete?(query: string, limit?: number): Promise<GeocodingResult[]>
}

/**
 * Nominatim (OpenStreetMap) geocoding provider
 * Free and open source, but has rate limits
 */
export class NominatimProvider implements GeocodingProvider {
  name = 'Nominatim'
  private baseUrl = 'https://nominatim.openstreetmap.org'
  private userAgent = 'BenefitLens/1.0'

  async geocode(location: string): Promise<GeocodingResult | null> {
    try {
      const params = new URLSearchParams({
        q: location,
        format: 'json',
        limit: '1',
        addressdetails: '1',
        extratags: '1',
      })

      const response = await fetch(`${this.baseUrl}/search?${params}`, {
        headers: {
          'User-Agent': this.userAgent,
        },
      })

      if (!response.ok) {
        throw new Error(`Nominatim API error: ${response.status}`)
      }

      const data = await response.json()
      
      if (!data || data.length === 0) {
        return null
      }

      const result = data[0]
      
      return {
        formatted_address: result.display_name,
        city: result.address?.city || result.address?.town || result.address?.village || '',
        country: result.address?.country || '',
        country_code: result.address?.country_code?.toUpperCase() || '',
        latitude: parseFloat(result.lat),
        longitude: parseFloat(result.lon),
        confidence: parseFloat(result.importance || '0.5'),
      }
    } catch (error) {
      console.error('Nominatim geocoding error:', error)
      return null
    }
  }

  async autocomplete(query: string, limit: number = 5): Promise<GeocodingResult[]> {
    try {
      const params = new URLSearchParams({
        q: query,
        format: 'json',
        limit: limit.toString(),
        addressdetails: '1',
        extratags: '1',
        featuretype: 'city',
      })

      const response = await fetch(`${this.baseUrl}/search?${params}`, {
        headers: {
          'User-Agent': this.userAgent,
        },
      })

      if (!response.ok) {
        throw new Error(`Nominatim API error: ${response.status}`)
      }

      const data = await response.json()
      
      return data.map((result: any) => ({
        formatted_address: result.display_name,
        city: result.address?.city || result.address?.town || result.address?.village || '',
        country: result.address?.country || '',
        country_code: result.address?.country_code?.toUpperCase() || '',
        latitude: parseFloat(result.lat),
        longitude: parseFloat(result.lon),
        confidence: parseFloat(result.importance || '0.5'),
      }))
    } catch (error) {
      console.error('Nominatim autocomplete error:', error)
      return []
    }
  }
}

/**
 * Google Places API provider (requires API key)
 * More accurate but costs money
 */
export class GooglePlacesProvider implements GeocodingProvider {
  name = 'Google Places'
  private apiKey: string

  constructor(apiKey: string) {
    this.apiKey = apiKey
  }

  async geocode(location: string): Promise<GeocodingResult | null> {
    try {
      const params = new URLSearchParams({
        address: location,
        key: this.apiKey,
      })

      const response = await fetch(`https://maps.googleapis.com/maps/api/geocode/json?${params}`)
      
      if (!response.ok) {
        throw new Error(`Google Geocoding API error: ${response.status}`)
      }

      const data = await response.json()
      
      if (data.status !== 'OK' || !data.results || data.results.length === 0) {
        return null
      }

      const result = data.results[0]
      const addressComponents = result.address_components || []
      
      const city = addressComponents.find((c: any) => 
        c.types.includes('locality') || c.types.includes('administrative_area_level_1')
      )?.long_name || ''
      
      const country = addressComponents.find((c: any) => 
        c.types.includes('country')
      )?.long_name || ''
      
      const countryCode = addressComponents.find((c: any) => 
        c.types.includes('country')
      )?.short_name || ''

      return {
        formatted_address: result.formatted_address,
        city,
        country,
        country_code: countryCode,
        latitude: result.geometry.location.lat,
        longitude: result.geometry.location.lng,
        confidence: 1.0, // Google results are generally high confidence
      }
    } catch (error) {
      console.error('Google Places geocoding error:', error)
      return null
    }
  }
}

/**
 * Geocoding service that manages multiple providers
 */
export class GeocodingService {
  private providers: GeocodingProvider[] = []
  private cache = new Map<string, GeocodingResult>()

  constructor() {
    // Add Nominatim as default free provider
    this.providers.push(new NominatimProvider())

    // Add Google Places if API key is available
    const googleApiKey = process.env.GOOGLE_PLACES_API_KEY
    if (googleApiKey) {
      this.providers.push(new GooglePlacesProvider(googleApiKey))
    }
  }

  /**
   * Geocode a location using available providers
   */
  async geocode(location: string): Promise<GeocodingResult | null> {
    const cacheKey = location.toLowerCase().trim()
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!
    }

    // Try each provider until we get a result
    for (const provider of this.providers) {
      try {
        const result = await provider.geocode(location)
        if (result) {
          // Cache the result
          this.cache.set(cacheKey, result)
          
          // Note: Location data is now handled by the comprehensive city library
          
          return result
        }
      } catch (error) {
        console.warn(`Provider ${provider.name} failed:`, error)
        continue
      }
    }

    return null
  }

  /**
   * Get autocomplete suggestions
   */
  async autocomplete(query: string, limit: number = 5): Promise<GeocodingResult[]> {
    const results: GeocodingResult[] = []

    for (const provider of this.providers) {
      if (provider.autocomplete) {
        try {
          const suggestions = await provider.autocomplete(query, limit)
          results.push(...suggestions)
          
          // If we have enough results, stop
          if (results.length >= limit) {
            break
          }
        } catch (error) {
          console.warn(`Provider ${provider.name} autocomplete failed:`, error)
          continue
        }
      }
    }

    // Remove duplicates and limit results
    const uniqueResults = results.filter((result, index, self) => 
      index === self.findIndex(r => 
        r.city === result.city && r.country === result.country
      )
    )

    return uniqueResults.slice(0, limit)
  }

  /**
   * Format a normalized location string
   */
  private formatNormalizedLocation(city: string, country: string): string {
    if (!city && !country) return 'Unknown Location'
    if (!country) return city
    if (!city) return country
    return `${city}, ${country}`
  }
}

// Export singleton instance
export const geocodingService = new GeocodingService()
