import { query } from '@/lib/local-db'
import { sendEmail, EmailOptions } from '@/lib/email'

export interface UserDiscoveryResult {
  companyId: string
  companyName: string
  domain: string
  discoveredUsers: string[]
  notificationsSent: number
  errors: string[]
}

/**
 * Discovers existing users with email domains matching a company
 * and sends them notification emails about the new company
 */
export async function discoverAndNotifyUsers(companyId: string): Promise<UserDiscoveryResult> {
  const result: UserDiscoveryResult = {
    companyId,
    companyName: '',
    domain: '',
    discoveredUsers: [],
    notificationsSent: 0,
    errors: []
  }

  try {
    // Get company details
    const companyResult = await query(
      'SELECT id, name, domain FROM companies WHERE id = $1',
      [companyId]
    )

    if (companyResult.rows.length === 0) {
      throw new Error('Company not found')
    }

    const company = companyResult.rows[0]
    result.companyName = company.name
    result.domain = company.domain

    if (!company.domain) {
      result.errors.push('Company has no domain configured')
      return result
    }

    // Find existing users with matching email domain who are not already associated with this company
    const usersResult = await query(
      `SELECT email, first_name, last_name
       FROM users
       WHERE email LIKE $1
       AND (company_id IS NULL OR company_id != $2)`,
      [`%@${company.domain}`, companyId]
    )

    result.discoveredUsers = usersResult.rows.map(user => user.email)

    if (result.discoveredUsers.length === 0) {
      return result
    }

    // Associate users with company and send notification emails
    for (const user of usersResult.rows) {
      try {
        // Associate user with company via users.company_id
        await query(
          'UPDATE users SET company_id = $1 WHERE email = $2',
          [companyId, user.email.toLowerCase()]
        )

        // Send notification email
        const emailOptions = createCompanyDiscoveryEmail(
          user.email,
          user.first_name || 'User',
          company.name,
          company.domain,
          companyId
        )

        await sendEmail(emailOptions)
        result.notificationsSent++
      } catch (error) {
        result.errors.push(`Failed to process user ${user.email}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    return result

  } catch (error) {
    result.errors.push(error instanceof Error ? error.message : 'Unknown error')
    return result
  }
}

/**
 * Creates an email notification for users when their company is added
 */
export function createCompanyDiscoveryEmail(
  userEmail: string,
  firstName: string,
  companyName: string,
  companyDomain: string,
  companyId: string
): EmailOptions {
  const companyUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/companies/${companyId}`
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Your Company is Now on BenefitLens!</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px 20px; background: #f9fafb; }
        .button { 
          display: inline-block; 
          background: #2563eb; 
          color: white; 
          padding: 12px 24px; 
          text-decoration: none; 
          border-radius: 6px; 
          margin: 20px 0;
        }
        .highlight { background: #e0f2fe; padding: 15px; border-radius: 6px; margin: 15px 0; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>BenefitLens</h1>
          <h2>🎉 Great News!</h2>
        </div>
        
        <div class="content">
          <p>Hello ${firstName},</p>
          
          <div class="highlight">
            <h3>Your company <strong>${companyName}</strong> is now available on BenefitLens!</h3>
          </div>
          
          <p>We noticed you have an email address with the domain <strong>@${companyDomain}</strong>, which matches ${companyName} in our platform.</p>
          
          <p><strong>What this means for you:</strong></p>
          <ul>
            <li>🏢 You can now view and verify benefits for ${companyName}</li>
            <li>✅ Help other employees by confirming which benefits are accurate</li>
            <li>🚨 Report any incorrect benefit information</li>
            <li>📊 See what benefits other companies offer for comparison</li>
          </ul>
          
          <p><strong>Next Steps:</strong></p>
          <ol>
            <li>Visit your company page to see the current benefits</li>
            <li>Verify your company association using your work email</li>
            <li>Start verifying benefits to help build accurate data</li>
          </ol>
          
          <div style="text-align: center;">
            <a href="${companyUrl}" class="button">View ${companyName} on BenefitLens</a>
          </div>
          
          <p><strong>Important:</strong> To verify or dispute benefits, you'll need to confirm your association with ${companyName} using your work email address.</p>
          
          <p>If you're not actually employed by ${companyName}, you can safely ignore this email.</p>
          
          <p>Welcome to BenefitLens!<br>The BenefitLens Team</p>
        </div>
        
        <div class="footer">
          <p>This email was sent to ${userEmail}</p>
          <p>BenefitLens - Company Benefits Verification Platform</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
    BenefitLens - Your Company is Now Available!
    
    Hello ${firstName},
    
    Great news! Your company ${companyName} is now available on BenefitLens!
    
    We noticed you have an email address with the domain @${companyDomain}, which matches ${companyName} in our platform.
    
    What this means for you:
    - You can now view and verify benefits for ${companyName}
    - Help other employees by confirming which benefits are accurate
    - Report any incorrect benefit information
    - See what benefits other companies offer for comparison
    
    Next Steps:
    1. Visit your company page: ${companyUrl}
    2. Verify your company association using your work email
    3. Start verifying benefits to help build accurate data
    
    Important: To verify or dispute benefits, you'll need to confirm your association with ${companyName} using your work email address.
    
    If you're not actually employed by ${companyName}, you can safely ignore this email.
    
    Welcome to BenefitLens!
    The BenefitLens Team
    
    This email was sent to ${userEmail}
  `

  return {
    to: userEmail,
    subject: `🎉 ${companyName} is now on BenefitLens - Verify your benefits!`,
    html,
    text,
  }
}
