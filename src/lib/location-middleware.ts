import { NextRequest } from 'next/server'
import { normalizeLocation, type LocationData } from './location-normalization'

/**
 * Location normalization middleware for API endpoints
 * Automatically normalizes location inputs to country-state-city format
 */

export interface NormalizedLocationInput {
  location_raw: string
  location_normalized: string
  city: string
  country: string
  country_code: string
  latitude?: number
  longitude?: number
}

/**
 * Normalizes a single location input
 */
export async function normalizeLocationInput(locationRaw: string): Promise<NormalizedLocationInput | null> {
  if (!locationRaw || locationRaw.trim() === '') {
    return null
  }

  try {
    const normalized = await normalizeLocation(locationRaw.trim())
    
    if (!normalized) {
      console.warn(`Could not normalize location: ${locationRaw}`)
      return null
    }

    return {
      location_raw: locationRaw.trim(),
      location_normalized: normalized.normalized,
      city: normalized.city,
      country: normalized.country,
      country_code: normalized.countryCode,
      latitude: normalized.latitude,
      longitude: normalized.longitude
    }
  } catch (error) {
    console.error(`Error normalizing location "${locationRaw}":`, error)
    return null
  }
}

/**
 * Normalizes multiple location inputs
 */
export async function normalizeLocationInputs(locations: string[]): Promise<NormalizedLocationInput[]> {
  const results: NormalizedLocationInput[] = []
  
  for (const location of locations) {
    const normalized = await normalizeLocationInput(location)
    if (normalized) {
      results.push(normalized)
    }
  }
  
  return results
}

/**
 * Middleware function to normalize location data in request body
 * Handles both single location and multiple locations
 */
export async function withLocationNormalization<T extends Record<string, any>>(
  request: NextRequest,
  handler: (normalizedData: T) => Promise<Response>
): Promise<Response> {
  try {
    const body = await request.json()
    const normalizedData = { ...body }

    // Handle single location field
    if (body.location && typeof body.location === 'string') {
      const normalized = await normalizeLocationInput(body.location)
      if (normalized) {
        normalizedData.location_raw = normalized.location_raw
        normalizedData.location_normalized = normalized.location_normalized
        normalizedData.city = normalized.city
        normalizedData.country = normalized.country
        normalizedData.country_code = normalized.country_code
        normalizedData.latitude = normalized.latitude
        normalizedData.longitude = normalized.longitude
      }
    }

    // Handle location_raw field
    if (body.location_raw && typeof body.location_raw === 'string') {
      const normalized = await normalizeLocationInput(body.location_raw)
      if (normalized) {
        Object.assign(normalizedData, normalized)
      }
    }

    // Handle multiple locations array
    if (body.locations && Array.isArray(body.locations)) {
      const normalizedLocations = []
      
      for (const location of body.locations) {
        if (typeof location === 'string') {
          const normalized = await normalizeLocationInput(location)
          if (normalized) {
            normalizedLocations.push(normalized)
          }
        } else if (location && typeof location === 'object' && location.location_raw) {
          const normalized = await normalizeLocationInput(location.location_raw)
          if (normalized) {
            normalizedLocations.push({
              ...location,
              ...normalized
            })
          } else {
            normalizedLocations.push(location)
          }
        } else {
          normalizedLocations.push(location)
        }
      }
      
      normalizedData.locations = normalizedLocations
    }

    return await handler(normalizedData as T)
  } catch (error) {
    console.error('Location normalization middleware error:', error)
    return new Response(
      JSON.stringify({ error: 'Invalid request data' }),
      { status: 400, headers: { 'Content-Type': 'application/json' } }
    )
  }
}

/**
 * Validates that a location has been properly normalized
 */
export function validateNormalizedLocation(location: any): location is NormalizedLocationInput {
  return (
    location &&
    typeof location.location_raw === 'string' &&
    typeof location.location_normalized === 'string' &&
    typeof location.city === 'string' &&
    typeof location.country === 'string' &&
    typeof location.country_code === 'string' &&
    location.location_raw.trim() !== '' &&
    location.city.trim() !== '' &&
    location.country.trim() !== ''
  )
}

/**
 * Helper function to extract location query parameter and normalize it
 */
export async function normalizeLocationQuery(request: NextRequest): Promise<LocationData | null> {
  const { searchParams } = new URL(request.url)
  const locationQuery = searchParams.get('location')

  if (!locationQuery) {
    return null
  }

  try {
    return await normalizeLocation(locationQuery)
  } catch (error) {
    console.error('Error normalizing location query:', error)
    return null
  }
}

/**
 * Middleware for search endpoints that need location normalization
 */
export async function withLocationSearch<T extends Record<string, any>>(
  request: NextRequest,
  handler: (params: T & { normalizedLocation?: LocationData }) => Promise<Response>
): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url)
    const params = Object.fromEntries(searchParams.entries()) as T
    
    const normalizedLocation = await normalizeLocationQuery(request)

    return await handler({
      ...params,
      normalizedLocation: normalizedLocation || undefined
    })
  } catch (error) {
    console.error('Location search middleware error:', error)
    return new Response(
      JSON.stringify({ error: 'Invalid search parameters' }),
      { status: 400, headers: { 'Content-Type': 'application/json' } }
    )
  }
}
