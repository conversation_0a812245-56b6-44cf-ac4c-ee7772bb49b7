/**
 * Demo Analytics Data Generator
 * Generates realistic but clearly marked sample data for non-paying users
 */

export interface DemoCompanyAnalytics {
  company: {
    id: string
    name: string
    location: string
    industry: string
    verified: boolean
  }
  period: string
  overview: {
    total_views: number
    total_searches: number
    total_benefits: number
    verified_benefits: number
    avg_daily_views: number
    engagement_rate: number
  }
  time_series: Array<{
    date: string
    views: number
    searches: number
  }>
  top_benefits: Array<{
    name: string
    category: string
    icon?: string
    is_verified: boolean
    views: number
    interactions: number
    performance_score: number
  }>
  category_breakdown: Array<{
    category: string
    count: number
    percentage: number
  }>
  benefit_performance: Array<{
    name: string
    category: string
    icon?: string
    is_verified: boolean
    views: number
    interactions: number
    performance_score: number
  }>
  generated_at: string
  is_demo_data: boolean
  demo_notice: string
}

export interface DemoSearchTrends {
  trends: Array<{
    rank: number
    search_term: string
    category: string
    search_count: number
    trend_score: number
    change: number
    icon?: string
  }>
  period: string
  total_searches: number
  is_demo_data: boolean
  demo_notice: string
}

export interface DemoTopCompanies {
  companies: Array<{
    rank: number
    id: string
    name: string
    location: string
    industry: string
    verified: boolean
    view_count: number
    benefit_count: number
    verified_benefit_count: number
    engagement_rate: number
    benefit_completion_rate: number
    top_benefits: Array<{
      name: string
      icon?: string
      verified: boolean
    }>
  }>
  period: string
  total_companies: number
  is_demo_data: boolean
  demo_notice: string
}

// Sample benefit categories and names for demo data
const DEMO_BENEFITS = [
  { name: 'Health Insurance', category: 'health', icon: '🏥' },
  { name: 'Dental Coverage', category: 'health', icon: '🦷' },
  { name: 'Vision Insurance', category: 'health', icon: '👁️' },
  { name: 'Paid Time Off', category: 'time_off', icon: '🏖️' },
  { name: 'Sick Leave', category: 'time_off', icon: '🤒' },
  { name: '401k Matching', category: 'financial', icon: '💰' },
  { name: 'Stock Options', category: 'financial', icon: '📈' },
  { name: 'Professional Development', category: 'development', icon: '📚' },
  { name: 'Conference Budget', category: 'development', icon: '🎤' },
  { name: 'Gym Membership', category: 'wellness', icon: '💪' },
  { name: 'Mental Health Support', category: 'wellness', icon: '🧠' },
  { name: 'Remote Work', category: 'work_life', icon: '🏠' },
  { name: 'Flexible Hours', category: 'work_life', icon: '⏰' },
  { name: 'Parental Leave', category: 'time_off', icon: '👶' },
  { name: 'Life Insurance', category: 'financial', icon: '🛡️' }
]

const DEMO_COMPANIES = [
  { name: 'TechCorp Solutions', location: 'San Francisco, CA', industry: 'Technology' },
  { name: 'InnovateLabs', location: 'Austin, TX', industry: 'Software' },
  { name: 'DataDriven Inc', location: 'Seattle, WA', industry: 'Analytics' },
  { name: 'CloudFirst Systems', location: 'Denver, CO', industry: 'Cloud Services' },
  { name: 'NextGen Robotics', location: 'Boston, MA', industry: 'Robotics' },
  { name: 'GreenTech Energy', location: 'Portland, OR', industry: 'Clean Energy' },
  { name: 'FinanceFlow', location: 'New York, NY', industry: 'Financial Services' },
  { name: 'HealthTech Plus', location: 'Chicago, IL', industry: 'Healthcare' },
  { name: 'EduSmart Platform', location: 'Raleigh, NC', industry: 'Education' },
  { name: 'RetailRevolution', location: 'Los Angeles, CA', industry: 'E-commerce' }
]

const DEMO_NOTICE = "This is sample data for demonstration purposes. Upgrade to Premium to access real analytics data."

/**
 * Generate demo benefit ranking analytics data
 */
export function generateDemoBenefitRankings(period: string = '30d', limit: number = 20, category?: string | null) {
  // Filter benefits by category if specified
  let filteredBenefits = DEMO_BENEFITS
  if (category) {
    filteredBenefits = DEMO_BENEFITS.filter(b => b.category === category)
  }

  // Generate ranking statistics for each benefit
  const benefitStats = filteredBenefits
    .slice(0, limit)
    .map((benefit, _index) => {
      const totalRankings = Math.floor(Math.random() * 200) + 50 // 50-250 rankings
      const averageRanking = Math.random() * 8 + 1.5 // 1.5-9.5 average
      const bestRanking = Math.floor(Math.random() * 3) + 1 // 1-3
      const worstRanking = Math.floor(Math.random() * 3) + 8 // 8-10
      const top3Count = Math.floor(totalRankings * (Math.random() * 0.4 + 0.1)) // 10-50% in top 3
      const bottom3Count = Math.floor(totalRankings * (Math.random() * 0.3 + 0.05)) // 5-35% in bottom 3

      return {
        benefit_id: `demo-${benefit.name.toLowerCase().replace(/\s+/g, '-')}`,
        benefit_name: benefit.name,
        category: benefit.category,
        icon: benefit.icon,
        category_display_name: benefit.category.charAt(0).toUpperCase() + benefit.category.slice(1).replace('_', ' '),
        total_rankings: totalRankings,
        average_ranking: Math.round(averageRanking * 100) / 100,
        best_ranking: bestRanking,
        worst_ranking: worstRanking,
        top_3_count: top3Count,
        bottom_3_count: bottom3Count,
        ranking_stddev: Math.round((Math.random() * 2 + 1) * 100) / 100,
        importance_score: Math.round((11 - averageRanking) * 10)
      }
    })
    .sort((a, b) => a.average_ranking - b.average_ranking)

  // Generate trend data
  const days = period === '7d' ? 7 : period === '30d' ? 30 : 90
  const trends: Array<{
    date: string
    benefit_name: string
    average_ranking: number
    ranking_count: number
  }> = []
  const now = new Date()

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now)
    date.setDate(date.getDate() - i)

    // Add some trends for top benefits
    benefitStats.slice(0, 5).forEach(benefit => {
      trends.push({
        date: date.toISOString().split('T')[0],
        benefit_name: benefit.benefit_name,
        average_ranking: Math.round((benefit.average_ranking + (Math.random() - 0.5) * 0.5) * 100) / 100,
        ranking_count: Math.floor(Math.random() * 20) + 5
      })
    })
  }

  // Generate category breakdown
  const categoryBreakdown = Object.entries(
    benefitStats.reduce((acc, benefit) => {
      if (!acc[benefit.category]) {
        acc[benefit.category] = {
          total_rankings: 0,
          rankings_sum: 0,
          top_3_count: 0,
          count: 0
        }
      }
      acc[benefit.category].total_rankings += benefit.total_rankings
      acc[benefit.category].rankings_sum += benefit.average_ranking * benefit.total_rankings
      acc[benefit.category].top_3_count += benefit.top_3_count
      acc[benefit.category].count += 1
      return acc
    }, {} as Record<string, any>)
  ).map(([category, data]) => ({
    category,
    category_display_name: category.charAt(0).toUpperCase() + category.slice(1).replace('_', ' '),
    total_rankings: data.total_rankings,
    average_ranking: Math.round((data.rankings_sum / data.total_rankings) * 100) / 100,
    top_3_count: data.top_3_count
  }))

  // Generate improvement data
  const improvements = benefitStats.slice(0, 10).map(benefit => {
    const improvement = (Math.random() - 0.5) * 2 // -1 to +1
    return {
      benefit_name: benefit.benefit_name,
      category: benefit.category,
      icon: benefit.icon,
      current_avg: benefit.average_ranking,
      previous_avg: Math.round((benefit.average_ranking - improvement) * 100) / 100,
      improvement: Math.round(improvement * 100) / 100
    }
  }).sort((a, b) => b.improvement - a.improvement)

  const totalRankings = benefitStats.reduce((sum, b) => sum + b.total_rankings, 0)
  const avgOverallRanking = benefitStats.length > 0
    ? benefitStats.reduce((sum, b) => sum + b.average_ranking, 0) / benefitStats.length
    : 0

  return {
    period,
    category: category || 'all',
    summary: {
      totalRankings,
      totalBenefitsRanked: benefitStats.length,
      averageOverallRanking: Math.round(avgOverallRanking * 100) / 100,
      mostImportantBenefit: benefitStats[0]?.benefit_name || null,
      leastImportantBenefit: benefitStats[benefitStats.length - 1]?.benefit_name || null
    },
    benefitStats,
    trends,
    categoryBreakdown,
    improvements,
    generated_at: new Date().toISOString(),
    is_demo_data: true,
    demo_notice: DEMO_NOTICE
  }
}

/**
 * Generate demo company analytics data
 */
export function generateDemoCompanyAnalytics(companyId: string, period: string = '30d'): DemoCompanyAnalytics {
  const company = {
    id: companyId,
    name: DEMO_COMPANIES[Math.floor(Math.random() * DEMO_COMPANIES.length)].name,
    location: DEMO_COMPANIES[Math.floor(Math.random() * DEMO_COMPANIES.length)].location,
    industry: DEMO_COMPANIES[Math.floor(Math.random() * DEMO_COMPANIES.length)].industry,
    verified: Math.random() > 0.3
  }

  // Generate time series data
  const days = period === '7d' ? 7 : period === '30d' ? 30 : 90
  const time_series = []
  const now = new Date()
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now)
    date.setDate(date.getDate() - i)
    time_series.push({
      date: date.toISOString().split('T')[0],
      views: Math.floor(Math.random() * 150) + 50,
      searches: Math.floor(Math.random() * 75) + 25
    })
  }

  const totalViews = time_series.reduce((sum, day) => sum + day.views, 0)
  const totalSearches = time_series.reduce((sum, day) => sum + day.searches, 0)

  // Generate benefit performance data
  const benefitCount = Math.floor(Math.random() * 8) + 5 // 5-12 benefits
  const benefits = DEMO_BENEFITS
    .sort(() => Math.random() - 0.5)
    .slice(0, benefitCount)
    .map((benefit, _index) => ({
      ...benefit,
      is_verified: Math.random() > 0.4,
      views: Math.floor(Math.random() * 500) + 100,
      interactions: Math.floor(Math.random() * 100) + 20,
      performance_score: Math.floor(Math.random() * 100) + 50
    }))

  const verifiedBenefits = benefits.filter(b => b.is_verified).length

  // Category breakdown
  const categoryBreakdown = DEMO_BENEFITS.reduce((acc, benefit) => {
    if (!acc[benefit.category]) {
      acc[benefit.category] = { count: 0, percentage: 0 }
    }
    acc[benefit.category].count++
    return acc
  }, {} as Record<string, { count: number; percentage: number }>)

  Object.keys(categoryBreakdown).forEach(category => {
    categoryBreakdown[category].percentage = Math.round(
      (categoryBreakdown[category].count / benefitCount) * 100
    )
  })

  return {
    company,
    period,
    overview: {
      total_views: totalViews,
      total_searches: totalSearches,
      total_benefits: benefitCount,
      verified_benefits: verifiedBenefits,
      avg_daily_views: Math.round(totalViews / days),
      engagement_rate: Math.round((Math.random() * 25 + 70) * 100) / 100
    },
    time_series,
    top_benefits: benefits
      .sort((a, b) => b.performance_score - a.performance_score)
      .slice(0, 5),
    category_breakdown: Object.entries(categoryBreakdown).map(([category, data]) => ({
      category,
      ...data
    })),
    benefit_performance: benefits,
    generated_at: new Date().toISOString(),
    is_demo_data: true,
    demo_notice: DEMO_NOTICE
  }
}

/**
 * Generate demo search trends data
 */
export function generateDemoSearchTrends(period: string = '7d', limit: number = 10): DemoSearchTrends {
  const trends = DEMO_BENEFITS
    .sort(() => Math.random() - 0.5)
    .slice(0, limit)
    .map((benefit, index) => ({
      rank: index + 1,
      search_term: benefit.name,
      category: benefit.category,
      search_count: Math.floor(Math.random() * 1000) + 100,
      trend_score: Math.floor(Math.random() * 100) + 50,
      change: Math.floor(Math.random() * 40) - 20, // -20 to +20
      icon: benefit.icon
    }))
    .sort((a, b) => b.search_count - a.search_count)

  const totalSearches = trends.reduce((sum, trend) => sum + trend.search_count, 0)

  return {
    trends,
    period,
    total_searches: totalSearches,
    is_demo_data: true,
    demo_notice: DEMO_NOTICE
  }
}

/**
 * Generate demo top companies data
 */
export function generateDemoTopCompanies(period: string = '7d', limit: number = 10): DemoTopCompanies {
  const companies = DEMO_COMPANIES
    .sort(() => Math.random() - 0.5)
    .slice(0, limit)
    .map((company, index) => {
      const benefitCount = Math.floor(Math.random() * 10) + 3
      const verifiedBenefitCount = Math.floor(benefitCount * (Math.random() * 0.5 + 0.5))
      const topBenefits = DEMO_BENEFITS
        .sort(() => Math.random() - 0.5)
        .slice(0, 3)
        .map(benefit => ({
          name: benefit.name,
          icon: benefit.icon,
          verified: Math.random() > 0.3
        }))

      return {
        rank: index + 1,
        id: `demo-company-${index + 1}`,
        name: company.name,
        location: company.location,
        industry: company.industry,
        verified: Math.random() > 0.4,
        view_count: Math.floor(Math.random() * 5000) + 1000,
        benefit_count: benefitCount,
        verified_benefit_count: verifiedBenefitCount,
        engagement_rate: Math.round((Math.random() * 30 + 70) * 100) / 100,
        benefit_completion_rate: Math.round((verifiedBenefitCount / benefitCount) * 100),
        top_benefits: topBenefits
      }
    })
    .sort((a, b) => b.view_count - a.view_count)

  return {
    companies,
    period,
    total_companies: companies.length,
    is_demo_data: true,
    demo_notice: DEMO_NOTICE
  }
}
