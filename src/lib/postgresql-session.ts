// Enhanced PostgreSQL Session Management
// Optimized for high-performance session handling without Redis

import { query } from './local-db'
import { logger } from './logger'

export interface SessionData {
  userId: string
  expiresAt: string
  createdAt: string
}

export interface SessionStats {
  totalSessions: number
  expiredSessions: number
  activeSessions: number
  oldestSession: string | null
  newestSession: string | null
}

// Session management functions
export async function setSession(sessionToken: string, userId: string, expiresAt: Date): Promise<boolean> {
  try {
    // Check if user has too many active sessions
    const maxSessions = await getSessionConfig('max_sessions_per_user')
    if (maxSessions) {
      await enforceMaxSessionsPerUser(userId, parseInt(maxSessions))
    }

    const result = await query(
      `INSERT INTO user_sessions (user_id, session_token, expires_at)
       VALUES ($1, $2, $3)
       ON CONFLICT (session_token) 
       DO UPDATE SET expires_at = EXCLUDED.expires_at, user_id = EXCLUDED.user_id
       RETURNING id`,
      [userId, sessionToken, expiresAt]
    )

    if (result.rows.length > 0) {
      // Log session creation activity
      await logSessionActivity(sessionToken, 'login')
      logger.info('Session created successfully', { 
        sessionToken: sessionToken.substring(0, 8) + '...', 
        userId,
        expiresAt: expiresAt.toISOString()
      })
      return true
    }
    return false
  } catch (error) {
    logger.error('Error setting session in PostgreSQL', { error: error as Error, userId })
    return false
  }
}

export async function getSession(sessionToken: string): Promise<SessionData | null> {
  try {
    const result = await query(
      `SELECT u.id as user_id, us.expires_at, us.created_at
       FROM user_sessions us
       JOIN users u ON us.user_id = u.id
       WHERE us.session_token = $1 AND us.expires_at > NOW()`,
      [sessionToken]
    )

    if (result.rows.length === 0) {
      return null
    }

    const session = result.rows[0]
    
    // Log session activity
    await logSessionActivity(sessionToken, 'page_view')
    
    // Optionally extend session on activity
    const extensionHours = await getSessionConfig('session_extension_hours')
    if (extensionHours && parseInt(extensionHours) > 0) {
      await extendSession(sessionToken, parseInt(extensionHours))
    }

    return {
      userId: session.user_id,
      expiresAt: session.expires_at.toISOString(),
      createdAt: session.created_at.toISOString(),
    }
  } catch (error) {
    logger.error('Error getting session from PostgreSQL', { error: error as Error, sessionToken: sessionToken.substring(0, 8) + '...' })
    return null
  }
}

export async function deleteSession(sessionToken: string): Promise<boolean> {
  try {
    // Log logout activity before deletion
    await logSessionActivity(sessionToken, 'logout')
    
    const result = await query(
      'DELETE FROM user_sessions WHERE session_token = $1',
      [sessionToken]
    )

    logger.info('Session deleted', { 
      sessionToken: sessionToken.substring(0, 8) + '...',
      deleted: result.rowCount > 0
    })
    
    return result.rowCount > 0
  } catch (error) {
    logger.error('Error deleting session from PostgreSQL', { error: error as Error })
    return false
  }
}

export async function deleteAllUserSessions(userId: string): Promise<boolean> {
  try {
    const result = await query('SELECT delete_all_user_sessions($1)', [userId])
    const deletedCount = result.rows[0]?.delete_all_user_sessions || 0
    
    logger.info('All user sessions deleted', { userId, deletedCount })
    return true
  } catch (error) {
    logger.error('Error deleting all user sessions', { error: error as Error, userId })
    return false
  }
}

// Session cleanup and maintenance
export async function cleanupExpiredSessions(): Promise<number> {
  try {
    const result = await query('SELECT cleanup_expired_sessions()')
    const deletedCount = result.rows[0]?.cleanup_expired_sessions || 0
    
    logger.info('Expired sessions cleaned up', { deletedCount })
    return deletedCount
  } catch (error) {
    logger.error('Error cleaning up expired sessions', { error: error as Error })
    return 0
  }
}

export async function getSessionStats(): Promise<SessionStats | null> {
  try {
    const result = await query('SELECT * FROM get_active_session_stats()')
    
    if (result.rows.length === 0) {
      return null
    }

    const stats = result.rows[0]
    return {
      totalSessions: stats.total_sessions,
      expiredSessions: stats.expired_sessions,
      activeSessions: stats.active_sessions,
      oldestSession: stats.oldest_session?.toISOString() || null,
      newestSession: stats.newest_session?.toISOString() || null,
    }
  } catch (error) {
    logger.error('Error getting session stats', { error: error as Error })
    return null
  }
}

// Helper functions
async function enforceMaxSessionsPerUser(userId: string, maxSessions: number): Promise<void> {
  try {
    // Delete oldest sessions if user has too many
    await query(
      `DELETE FROM user_sessions 
       WHERE user_id = $1 
       AND session_token IN (
         SELECT session_token 
         FROM user_sessions 
         WHERE user_id = $1 AND expires_at > NOW()
         ORDER BY created_at ASC 
         LIMIT GREATEST(0, (
           SELECT COUNT(*) - $2 + 1
           FROM user_sessions 
           WHERE user_id = $1 AND expires_at > NOW()
         ))
       )`,
      [userId, maxSessions]
    )
  } catch (error) {
    logger.warn('Error enforcing max sessions per user', { error: error as Error, userId })
  }
}

async function extendSession(sessionToken: string, extensionHours: number): Promise<void> {
  try {
    await query(
      `UPDATE user_sessions 
       SET expires_at = expires_at + INTERVAL '${extensionHours} hours'
       WHERE session_token = $1 AND expires_at > NOW()`,
      [sessionToken]
    )
  } catch (error) {
    logger.warn('Error extending session', { error: error as Error })
  }
}

async function getSessionConfig(configName: string): Promise<string | null> {
  try {
    const result = await query('SELECT get_session_config($1)', [configName])
    return result.rows[0]?.get_session_config || null
  } catch (error) {
    logger.warn('Error getting session config', { error: error as Error, configName })
    return null
  }
}

async function logSessionActivity(sessionToken: string, activityType: string, ip?: string, userAgent?: string): Promise<void> {
  try {
    await query(
      'SELECT log_session_activity($1, $2, $3, $4)',
      [sessionToken, activityType, ip || null, userAgent || null]
    )
  } catch (error) {
    // Don't log errors for activity logging to avoid noise
    // logger.debug('Error logging session activity', { error: error as Error })
  }
}

// Scheduled cleanup function (to be called by cron or scheduled job)
export async function scheduledSessionCleanup(): Promise<void> {
  try {
    const deletedCount = await cleanupExpiredSessions()
    const stats = await getSessionStats()

    logger.info('Scheduled session cleanup completed', {
      deletedCount,
      activeSessions: stats?.activeSessions || 0
    })
  } catch (error) {
    logger.error('Error in scheduled session cleanup', { error: error as Error })
  }
}

// Cache management functions (PostgreSQL-based)
export async function setCache(key: string, value: any, ttlSeconds: number = 3600): Promise<boolean> {
  try {
    const result = await query('SELECT set_cache($1, $2, $3)', [key, JSON.stringify(value), ttlSeconds])
    return result.rows[0]?.set_cache || false
  } catch (error) {
    logger.error('Error setting cache in PostgreSQL', { error: error as Error, key })
    return false
  }
}

export async function getCache(key: string): Promise<any> {
  try {
    const result = await query('SELECT get_cache($1)', [key])
    const cachedData = result.rows[0]?.get_cache

    if (!cachedData) {
      return null
    }

    return JSON.parse(cachedData)
  } catch (error) {
    logger.error('Error getting cache from PostgreSQL', { error: error as Error, key })
    return null
  }
}

export async function deleteCache(key: string): Promise<boolean> {
  try {
    const result = await query('SELECT delete_cache($1)', [key])
    return result.rows[0]?.delete_cache || false
  } catch (error) {
    logger.error('Error deleting cache from PostgreSQL', { error: error as Error, key })
    return false
  }
}

export async function clearCachePattern(pattern: string): Promise<number> {
  try {
    const result = await query('SELECT clear_cache_pattern($1)', [pattern])
    return result.rows[0]?.clear_cache_pattern || 0
  } catch (error) {
    logger.error('Error clearing cache pattern from PostgreSQL', { error: error as Error, pattern })
    return 0
  }
}

export async function cleanupExpiredCache(): Promise<number> {
  try {
    const result = await query('SELECT cleanup_expired_cache()')
    return result.rows[0]?.cleanup_expired_cache || 0
  } catch (error) {
    logger.error('Error cleaning up expired cache', { error: error as Error })
    return 0
  }
}

// CSRF token management (PostgreSQL-based)
export async function setCSRFToken(sessionId: string, token: string, ttlSeconds: number = 3600): Promise<boolean> {
  try {
    const result = await query('SELECT set_csrf_token($1, $2, $3)', [sessionId, token, ttlSeconds])
    return result.rows[0]?.set_csrf_token || false
  } catch (error) {
    logger.error('Error setting CSRF token in PostgreSQL', { error: error as Error, sessionId })
    return false
  }
}

export async function getCSRFToken(sessionId: string): Promise<string | null> {
  try {
    const result = await query('SELECT get_csrf_token($1)', [sessionId])
    return result.rows[0]?.get_csrf_token || null
  } catch (error) {
    logger.error('Error getting CSRF token from PostgreSQL', { error: error as Error, sessionId })
    return null
  }
}

// Materialized view refresh
export async function refreshCacheViews(): Promise<void> {
  try {
    await query('SELECT refresh_cache_views()')
    logger.info('Cache materialized views refreshed successfully')
  } catch (error) {
    logger.error('Error refreshing cache views', { error: error as Error })
  }
}
