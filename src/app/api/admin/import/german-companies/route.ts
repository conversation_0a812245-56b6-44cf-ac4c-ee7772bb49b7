import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { query } from '@/lib/local-db'

// Validation constants
const VALID_SIZES = ['startup', 'small', 'medium', 'large', 'enterprise']
const GERMAN_DOMAIN_REGEX = /^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.de$/
const GERMAN_LOCATION_KEYWORDS = ['Germany', 'Deutschland', 'Berlin', 'Munich', 'Frankfurt', 'Hamburg', 'Cologne', 'Stuttgart', 'Düsseldorf', 'Dortmund', 'Essen', 'Leipzig', 'Bremen', 'Dresden', 'Hannover']
const STOCK_TICKER_REGEX = /^[A-Z0-9]{1,6}$/
const VALID_INDICES = ['DAX', 'MDAX']

interface CompanyImportData {
  name: string
  location: string
  size: string
  industry: string
  description?: string
  domain?: string
  career_url?: string
  verified?: boolean
  ticker?: string
  index_membership?: string
}

interface ImportResult {
  success: boolean
  imported: number
  skipped: number
  errors: number
  details: Array<{
    company: string
    status: 'imported' | 'skipped' | 'error'
    message: string
  }>
}

function validateCompanyData(company: CompanyImportData): string[] {
  const errors: string[] = []

  // Required fields
  if (!company.name || company.name.trim().length === 0) {
    errors.push('Company name is required')
  }

  if (!company.location || company.location.trim().length === 0) {
    errors.push('Location is required')
  }

  if (!company.size || !VALID_SIZES.includes(company.size)) {
    errors.push(`Size must be one of: ${VALID_SIZES.join(', ')}`)
  }

  if (!company.industry || company.industry.trim().length === 0) {
    errors.push('Industry is required')
  }

  // German domain validation (relaxed for DAX/MDAX companies)
  if (company.domain && !GERMAN_DOMAIN_REGEX.test(company.domain)) {
    // Allow international domains for DAX/MDAX listed companies
    if (!company.index_membership || !VALID_INDICES.includes(company.index_membership)) {
      errors.push('Domain must be a valid German domain (.de)')
    }
  }

  // German location validation (relaxed for DAX/MDAX companies)
  if (company.location && !GERMAN_LOCATION_KEYWORDS.some(keyword =>
    company.location.toLowerCase().includes(keyword.toLowerCase()))) {
    // Allow non-German locations for DAX/MDAX listed companies (e.g., ASML from Netherlands)
    if (!company.index_membership || !VALID_INDICES.includes(company.index_membership)) {
      errors.push('Location must be in Germany')
    }
  }

  // Stock ticker validation (optional field for DAX/MDAX companies)
  if (company.ticker && !STOCK_TICKER_REGEX.test(company.ticker)) {
    errors.push('Stock ticker must be 1-6 uppercase letters/numbers')
  }

  // Index membership validation (optional field)
  if (company.index_membership && !VALID_INDICES.includes(company.index_membership)) {
    errors.push(`Index membership must be one of: ${VALID_INDICES.join(', ')}`)
  }

  // URL validation
  if (company.career_url && !isValidUrl(company.career_url)) {
    errors.push('Career URL must be a valid URL')
  }

  return errors
}

function isValidUrl(string: string): boolean {
  try {
    new URL(string)
    return true
  } catch {
    return false
  }
}

async function checkDuplicateCompany(company: CompanyImportData) {
  const sql = `
    SELECT id, name, domain 
    FROM companies 
    WHERE LOWER(name) = LOWER($1) 
       OR (domain IS NOT NULL AND LOWER(domain) = LOWER($2))
  `
  const result = await query(sql, [company.name, company.domain || ''])
  return result.rows.length > 0 ? result.rows[0] : null
}

async function insertCompany(company: CompanyImportData) {
  const sql = `
    INSERT INTO companies (name, location, size, industry, description, domain, career_url)
    VALUES ($1, $2, $3, $4, $5, $6, $7)
    RETURNING id, name
  `
  
  // Enhance description with stock ticker and index information for DAX/MDAX companies
  let enhancedDescription = company.description?.trim() || ''
  if (company.ticker || company.index_membership) {
    const stockInfo = []
    if (company.index_membership) {
      stockInfo.push(`${company.index_membership}-listed`)
    }
    if (company.ticker) {
      stockInfo.push(`Stock: ${company.ticker}`)
    }
    if (stockInfo.length > 0) {
      enhancedDescription = stockInfo.join(' | ') + (enhancedDescription ? ` | ${enhancedDescription}` : '')
    }
  }

  const values = [
    company.name.trim(),
    company.location.trim(),
    company.size,
    company.industry.trim(),
    enhancedDescription || null,
    company.domain?.toLowerCase() || null,
    company.career_url || null
  ]

  const result = await query(sql, values)
  return result.rows[0]
}

export async function POST(request: NextRequest) {
  try {
    // Require admin access
    await requireAdmin()

    const body = await request.json()
    const { companies, dryRun = false } = body

    if (!Array.isArray(companies)) {
      return NextResponse.json(
        { error: 'Companies must be an array' },
        { status: 400 }
      )
    }

    const result: ImportResult = {
      success: true,
      imported: 0,
      skipped: 0,
      errors: 0,
      details: []
    }

    for (const company of companies) {
      try {
        // Validate company data
        const validationErrors = validateCompanyData(company)
        if (validationErrors.length > 0) {
          result.errors++
          result.details.push({
            company: company.name || 'Unknown',
            status: 'error',
            message: `Validation failed: ${validationErrors.join(', ')}`
          })
          continue
        }

        // Check for duplicates
        const duplicate = await checkDuplicateCompany(company)
        if (duplicate) {
          result.skipped++
          result.details.push({
            company: company.name,
            status: 'skipped',
            message: `Duplicate company (existing: ${duplicate.name})`
          })
          continue
        }

        // Insert company (unless dry run)
        if (!dryRun) {
          const insertedCompany = await insertCompany(company)
          result.imported++
          result.details.push({
            company: company.name,
            status: 'imported',
            message: `Successfully imported (ID: ${insertedCompany.id})`
          })
        } else {
          result.imported++
          result.details.push({
            company: company.name,
            status: 'imported',
            message: 'Would be imported (dry run)'
          })
        }

      } catch (error) {
        result.errors++
        result.details.push({
          company: company.name || 'Unknown',
          status: 'error',
          message: `Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }

    // Set success to false if there were any errors
    if (result.errors > 0) {
      result.success = false
    }

    return NextResponse.json(result)

  } catch (error) {
    console.error('German companies import error:', error)
    return NextResponse.json(
      { error: 'Failed to import companies' },
      { status: 500 }
    )
  }
}

// GET endpoint to retrieve the default German companies data
export async function GET() {
  try {
    await requireAdmin()

    // Return the same curated list from the script
    const germanCompanies = [
      {
        name: 'Volkswagen AG',
        location: 'Wolfsburg, Germany',
        size: 'enterprise',
        industry: 'Automotive',
        description: 'German multinational automotive manufacturing company',
        domain: 'volkswagen.de',
        career_url: 'https://www.volkswagen-karriere.de',
        verified: false
      },
      {
        name: 'BMW Group',
        location: 'Munich, Germany', 
        size: 'enterprise',
        industry: 'Automotive',
        description: 'German multinational corporation which produces luxury vehicles and motorcycles',
        domain: 'bmw.de',
        career_url: 'https://www.bmwgroup.jobs',
        verified: false
      },
      {
        name: 'Mercedes-Benz Group AG',
        location: 'Stuttgart, Germany',
        size: 'enterprise', 
        industry: 'Automotive',
        description: 'German multinational automotive corporation',
        domain: 'mercedes-benz.de',
        career_url: 'https://group.mercedes-benz.com/careers',
        verified: false
      },
      {
        name: 'Bosch',
        location: 'Stuttgart, Germany',
        size: 'enterprise',
        industry: 'Technology',
        description: 'German multinational engineering and technology company',
        domain: 'bosch.de',
        career_url: 'https://www.bosch.de/karriere',
        verified: false
      },
      {
        name: 'Adidas AG',
        location: 'Herzogenaurach, Germany',
        size: 'enterprise',
        industry: 'Consumer Goods',
        description: 'German multinational corporation that designs and manufactures shoes, clothing and accessories',
        domain: 'adidas.de',
        career_url: 'https://careers.adidas-group.com',
        verified: false
      },
      {
        name: 'BASF SE',
        location: 'Ludwigshafen, Germany',
        size: 'enterprise',
        industry: 'Chemicals',
        description: 'German multinational chemical company',
        domain: 'basf.de',
        career_url: 'https://www.basf.com/global/en/careers.html',
        verified: false
      },
      {
        name: 'Bayer AG',
        location: 'Leverkusen, Germany',
        size: 'enterprise',
        industry: 'Pharmaceuticals',
        description: 'German multinational pharmaceutical and life sciences company',
        domain: 'bayer.de',
        career_url: 'https://career.bayer.com',
        verified: false
      },
      {
        name: 'Deutsche Telekom AG',
        location: 'Bonn, Germany',
        size: 'enterprise',
        industry: 'Telecommunications',
        description: 'German telecommunications company',
        domain: 'telekom.de',
        career_url: 'https://www.telekom.com/en/careers',
        verified: false
      },
      {
        name: 'Allianz SE',
        location: 'Munich, Germany',
        size: 'enterprise',
        industry: 'Financial Services',
        description: 'German multinational financial services company',
        domain: 'allianz.de',
        career_url: 'https://careers.allianz.com',
        verified: false
      },
      {
        name: 'Zalando SE',
        location: 'Berlin, Germany',
        size: 'large',
        industry: 'E-commerce',
        description: 'German online fashion and lifestyle retailer',
        domain: 'zalando.de',
        career_url: 'https://jobs.zalando.com',
        verified: false
      }
    ]

    return NextResponse.json({
      companies: germanCompanies,
      count: germanCompanies.length
    })

  } catch (error) {
    console.error('Error fetching German companies data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch German companies data' },
      { status: 500 }
    )
  }
}
