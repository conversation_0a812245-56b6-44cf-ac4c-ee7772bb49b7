import { NextRequest, NextResponse } from 'next/server'
import { addCompanyBenefit, removeCompanyBenefit, getCompanyBenefits } from '@/lib/database'
import { requireAuth, canUserManageCompany } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: companyId } = await params
    const benefits = await getCompanyBenefits(companyId)
    return NextResponse.json(benefits)
  } catch (error) {
    console.error('Error fetching company benefits:', error)
    return NextResponse.json(
      { error: 'Failed to fetch company benefits' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const userId = await requireAuth()
    const { id: companyId } = await params

    // Check if user can manage this company based on email domain
    const canManage = await canUserManageCompany(companyId)
    if (!canManage) {
      return NextResponse.json(
        { error: 'You can only manage benefits for companies that match your email domain' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { benefitId } = body

    if (!benefitId) {
      return NextResponse.json(
        { error: 'Benefit ID is required' },
        { status: 400 }
      )
    }

    const benefit = await addCompanyBenefit(companyId, benefitId, userId)
    return NextResponse.json(benefit, { status: 201 })
  } catch (error) {
    console.error('Error adding company benefit:', error)
    return NextResponse.json(
      { error: 'Failed to add company benefit' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const _userId = await requireAuth()
    const { id: companyId } = await params

    // Check if user can manage this company based on email domain
    const canManage = await canUserManageCompany(companyId)
    if (!canManage) {
      return NextResponse.json(
        { error: 'You can only manage benefits for companies that match your email domain' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const benefitId = searchParams.get('benefitId')

    if (!benefitId) {
      return NextResponse.json(
        { error: 'Benefit ID is required' },
        { status: 400 }
      )
    }

    await removeCompanyBenefit(companyId, benefitId)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error removing company benefit:', error)
    return NextResponse.json(
      { error: 'Failed to remove company benefit' },
      { status: 500 }
    )
  }
}
