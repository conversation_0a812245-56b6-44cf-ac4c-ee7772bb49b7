/**
 * User Benefit Management Flow Tests
 * Tests for benefit ranking, adding/removing benefits, and benefit interactions
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock fetch for API calls
global.fetch = vi.fn()

// Mock database operations
vi.mock('@/lib/local-db', () => ({
  query: vi.fn(),
}))

const mockQuery = (await import('@/lib/local-db')).query as unknown as ReturnType<typeof vi.fn>

describe('User Benefit Management Flows', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(fetch as any).mockClear()
    mockQuery.mockReset()
  })

  describe('Personal Benefit Ranking', () => {
    it('should add benefit to personal ranking', async () => {
      const benefitData = {
        benefitId: 'benefit-123',
        position: 1
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          ranking: {
            id: 'ranking-123',
            userId: 'user-123',
            benefitId: 'benefit-123',
            position: 1
          }
        })
      })

      const response = await fetch('/api/user/benefit-ranking', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(benefitData)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.ranking.benefitId).toBe('benefit-123')
      expect(result.ranking.position).toBe(1)
    })

    it('should remove benefit from personal ranking', async () => {
      const benefitId = 'benefit-123'

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          message: 'Benefit removed from ranking'
        })
      })

      const response = await fetch(`/api/user/benefit-ranking/${benefitId}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.message).toBe('Benefit removed from ranking')
    })

    it('should reorder benefits in personal ranking', async () => {
      const reorderData = {
        rankings: [
          { benefitId: 'benefit-1', position: 1 },
          { benefitId: 'benefit-2', position: 2 },
          { benefitId: 'benefit-3', position: 3 }
        ]
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          rankings: reorderData.rankings
        })
      })

      const response = await fetch('/api/user/benefit-ranking/reorder', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(reorderData)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.rankings).toHaveLength(3)
      expect(result.rankings[0].position).toBe(1)
    })

    it('should reset personal benefit ranking', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          message: 'Benefit ranking reset successfully'
        })
      })

      const response = await fetch('/api/user/benefit-ranking/reset', {
        method: 'POST'
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.message).toBe('Benefit ranking reset successfully')
    })

    it('should get user benefit ranking', async () => {
      const mockRankings = [
        {
          id: 'ranking-1',
          benefitId: 'benefit-1',
          benefitName: 'Health Insurance',
          position: 1
        },
        {
          id: 'ranking-2',
          benefitId: 'benefit-2',
          benefitName: 'Remote Work',
          position: 2
        }
      ]

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          rankings: mockRankings
        })
      })

      const response = await fetch('/api/user/benefit-ranking')

      const result = await response.json()

      expect(result.rankings).toHaveLength(2)
      expect(result.rankings[0].position).toBe(1)
      expect(result.rankings[1].position).toBe(2)
    })
  })

  describe('Company Benefit Management', () => {
    it('should add benefit to company benefit list', async () => {
      const benefitData = {
        companyId: 'company-123',
        benefitId: 'benefit-456',
        isVerified: false
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          companyBenefit: {
            id: 'company-benefit-123',
            companyId: 'company-123',
            benefitId: 'benefit-456',
            isVerified: false
          }
        })
      })

      const response = await fetch('/api/companies/company-123/benefits', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(benefitData)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.companyBenefit.companyId).toBe('company-123')
      expect(result.companyBenefit.benefitId).toBe('benefit-456')
      expect(result.companyBenefit.isVerified).toBe(false)
    })

    it('should handle unauthorized benefit addition', async () => {
      const benefitData = {
        companyId: 'company-123',
        benefitId: 'benefit-456'
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 403,
        json: async () => ({ 
          error: 'Not authorized to manage this company' 
        })
      })

      const response = await fetch('/api/companies/company-123/benefits', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(benefitData)
      })

      const result = await response.json()

      expect(response.status).toBe(403)
      expect(result.error).toBe('Not authorized to manage this company')
    })
  })

  describe('Benefit Verification and Disputes', () => {
    it('should create benefit verification (confirm)', async () => {
      const verificationData = {
        companyBenefitId: 'company-benefit-123',
        status: 'confirmed',
        comment: 'This benefit is accurate'
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          verification: {
            id: 'verification-123',
            companyBenefitId: 'company-benefit-123',
            userId: 'user-123',
            status: 'confirmed',
            comment: 'This benefit is accurate'
          }
        })
      })

      const response = await fetch('/api/benefit-verifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(verificationData)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.verification.status).toBe('confirmed')
      expect(result.verification.comment).toBe('This benefit is accurate')
    })

    it('should create benefit dispute', async () => {
      const disputeData = {
        companyBenefitId: 'company-benefit-123',
        status: 'disputed',
        comment: 'This benefit is not offered anymore'
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          verification: {
            id: 'verification-124',
            companyBenefitId: 'company-benefit-123',
            userId: 'user-123',
            status: 'disputed',
            comment: 'This benefit is not offered anymore'
          }
        })
      })

      const response = await fetch('/api/benefit-verifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(disputeData)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.verification.status).toBe('disputed')
      expect(result.verification.comment).toBe('This benefit is not offered anymore')
    })

    it('should check authorization for benefit verification', async () => {
      const verificationData = {
        companyBenefitId: 'company-benefit-123'
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          canVerify: true,
          reason: 'User is associated with this company'
        })
      })

      const response = await fetch('/api/benefit-verifications/company-benefit-123/authorization')

      const result = await response.json()

      expect(result.canVerify).toBe(true)
      expect(result.reason).toBe('User is associated with this company')
    })

    it('should handle unauthorized verification attempt', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 403,
        json: async () => ({ 
          error: 'Not authorized to verify benefits for this company' 
        })
      })

      const response = await fetch('/api/benefit-verifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          companyBenefitId: 'company-benefit-123',
          status: 'confirmed'
        })
      })

      const result = await response.json()

      expect(response.status).toBe(403)
      expect(result.error).toBe('Not authorized to verify benefits for this company')
    })
  })

  describe('Browse Benefits', () => {
    it('should get all available benefits', async () => {
      const mockBenefits = [
        {
          id: 'benefit-1',
          name: 'Health Insurance',
          category: 'Health',
          icon: '🏥',
          description: 'Comprehensive health coverage'
        },
        {
          id: 'benefit-2',
          name: 'Remote Work',
          category: 'Flexibility',
          icon: '🏠',
          description: 'Work from anywhere'
        }
      ]

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          benefits: mockBenefits
        })
      })

      const response = await fetch('/api/benefits')

      const result = await response.json()

      expect(result.benefits).toHaveLength(2)
      expect(result.benefits[0].name).toBe('Health Insurance')
      expect(result.benefits[1].name).toBe('Remote Work')
    })

    it('should filter benefits by category', async () => {
      const mockHealthBenefits = [
        {
          id: 'benefit-1',
          name: 'Health Insurance',
          category: 'Health',
          icon: '🏥'
        }
      ]

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          benefits: mockHealthBenefits
        })
      })

      const response = await fetch('/api/benefits?category=Health')

      const result = await response.json()

      expect(result.benefits).toHaveLength(1)
      expect(result.benefits[0].category).toBe('Health')
    })

    it('should search benefits by name', async () => {
      const mockSearchResults = [
        {
          id: 'benefit-1',
          name: 'Health Insurance',
          category: 'Health',
          icon: '🏥'
        }
      ]

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          benefits: mockSearchResults
        })
      })

      const response = await fetch('/api/benefits?search=health')

      const result = await response.json()

      expect(result.benefits).toHaveLength(1)
      expect(result.benefits[0].name).toBe('Health Insurance')
    })
  })
})
