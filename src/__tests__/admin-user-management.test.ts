/**
 * Admin User Management Flow Tests
 * Tests for admin user operations, user permissions, and user analytics
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock fetch for API calls
global.fetch = vi.fn()

// Mock database operations
vi.mock('@/lib/local-db', () => ({
  query: vi.fn(),
}))

const mockQuery = (await import('@/lib/local-db')).query as unknown as ReturnType<typeof vi.fn>

describe('Admin User Management Flows', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(fetch as any).mockClear()
    mockQuery.mockReset()
  })

  describe('User Listing and Search', () => {
    it('should get all users with pagination', async () => {
      const mockUsers = [
        {
          id: 'user-1',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          role: 'user',
          isActive: true,
          createdAt: '2024-01-01T12:00:00Z',
          lastLoginAt: '2024-01-15T10:00:00Z',
          company: { name: 'Tech Corp' }
        },
        {
          id: 'user-2',
          email: '<EMAIL>',
          firstName: 'Jane',
          lastName: 'Smith',
          role: 'user',
          isActive: true,
          createdAt: '2024-01-02T12:00:00Z',
          lastLoginAt: '2024-01-14T15:00:00Z',
          company: { name: 'Design Studio' }
        }
      ]

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          users: mockUsers,
          total: 2,
          page: 1,
          limit: 20,
          totalPages: 1
        })
      })

      const response = await fetch('/api/admin/users?page=1&limit=20', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.users).toHaveLength(2)
      expect(result.total).toBe(2)
      expect(result.users[0].email).toBe('<EMAIL>')
      expect(result.users[1].email).toBe('<EMAIL>')
    })

    it('should search users by email', async () => {
      const mockUsers = [
        {
          id: 'user-1',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe'
        }
      ]

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          users: mockUsers,
          total: 1
        })
      })

      const response = await fetch('/api/admin/users?search=<EMAIL>', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.users).toHaveLength(1)
      expect(result.users[0].email).toBe('<EMAIL>')
    })

    it('should filter users by company', async () => {
      const mockUsers = [
        {
          id: 'user-1',
          email: '<EMAIL>',
          company: { name: 'Tech Corp', id: 'company-1' }
        }
      ]

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          users: mockUsers,
          total: 1
        })
      })

      const response = await fetch('/api/admin/users?company=company-1', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.users).toHaveLength(1)
      expect(result.users[0].company.name).toBe('Tech Corp')
    })

    it('should filter users by role', async () => {
      const mockAdmins = [
        {
          id: 'admin-1',
          email: '<EMAIL>',
          role: 'admin'
        }
      ]

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          users: mockAdmins,
          total: 1
        })
      })

      const response = await fetch('/api/admin/users?role=admin', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.users).toHaveLength(1)
      expect(result.users[0].role).toBe('admin')
    })
  })

  describe('User Details and Profile Management', () => {
    it('should get user details', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'user',
        isActive: true,
        createdAt: '2024-01-01T12:00:00Z',
        lastLoginAt: '2024-01-15T10:00:00Z',
        company: {
          id: 'company-1',
          name: 'Tech Corp'
        },
        benefitRankings: [
          { benefit: 'Health Insurance', position: 1 },
          { benefit: 'Remote Work', position: 2 }
        ],
        savedCompanies: [
          { id: 'company-2', name: 'Design Studio' }
        ],
        activityStats: {
          totalLogins: 25,
          benefitInteractions: 15,
          companyViews: 8
        }
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockUser
      })

      const response = await fetch('/api/admin/users/user-123', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.id).toBe('user-123')
      expect(result.email).toBe('<EMAIL>')
      expect(result.benefitRankings).toHaveLength(2)
      expect(result.savedCompanies).toHaveLength(1)
      expect(result.activityStats.totalLogins).toBe(25)
    })

    it('should handle non-existent user', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ 
          error: 'User not found' 
        })
      })

      const response = await fetch('/api/admin/users/nonexistent', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(response.status).toBe(404)
      expect(result.error).toBe('User not found')
    })
  })

  describe('User Account Management', () => {
    it('should update user information', async () => {
      const updateData = {
        firstName: 'Jonathan',
        lastName: 'Doe',
        role: 'admin'
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          user: {
            id: 'user-123',
            ...updateData,
            updatedAt: new Date().toISOString()
          }
        })
      })

      const response = await fetch('/api/admin/users/user-123', {
        method: 'PATCH',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(updateData)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.user.firstName).toBe('Jonathan')
      expect(result.user.role).toBe('admin')
    })

    it('should deactivate user account', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          user: {
            id: 'user-123',
            isActive: false,
            deactivatedAt: new Date().toISOString(),
            deactivatedBy: 'admin-123'
          }
        })
      })

      const response = await fetch('/api/admin/users/user-123/deactivate', {
        method: 'POST',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.user.isActive).toBe(false)
      expect(result.user.deactivatedBy).toBe('admin-123')
    })

    it('should reactivate user account', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          user: {
            id: 'user-123',
            isActive: true,
            reactivatedAt: new Date().toISOString(),
            reactivatedBy: 'admin-123'
          }
        })
      })

      const response = await fetch('/api/admin/users/user-123/reactivate', {
        method: 'POST',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.user.isActive).toBe(true)
      expect(result.user.reactivatedBy).toBe('admin-123')
    })

    it('should delete user account', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          message: 'User deleted successfully',
          cleanupActions: [
            'Removed benefit rankings',
            'Removed saved companies',
            'Anonymized analytics data'
          ]
        })
      })

      const response = await fetch('/api/admin/users/user-123', {
        method: 'DELETE',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.message).toBe('User deleted successfully')
      expect(result.cleanupActions).toHaveLength(3)
    })
  })

  describe('User Company Associations', () => {
    it('should update user company association', async () => {
      const associationData = {
        companyId: 'company-456',
        role: 'employee'
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          association: {
            userId: 'user-123',
            companyId: 'company-456',
            role: 'employee',
            updatedAt: new Date().toISOString()
          }
        })
      })

      const response = await fetch('/api/admin/users/user-123/company-association', {
        method: 'PUT',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(associationData)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.association.companyId).toBe('company-456')
      expect(result.association.role).toBe('employee')
    })

    it('should remove user company association', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          message: 'Company association removed'
        })
      })

      const response = await fetch('/api/admin/users/user-123/company-association', {
        method: 'DELETE',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.message).toBe('Company association removed')
    })
  })

  describe('User Analytics and Activity', () => {
    it('should get user analytics dashboard', async () => {
      const mockAnalytics = {
        totalUsers: 1250,
        activeUsers: 1180,
        inactiveUsers: 70,
        newUsersThisMonth: 45,
        usersByRole: {
          user: 1200,
          admin: 50
        },
        usersByCompany: [
          { company: 'Tech Corp', userCount: 25 },
          { company: 'Design Studio', userCount: 18 }
        ],
        activityStats: {
          dailyActiveUsers: 320,
          weeklyActiveUsers: 850,
          monthlyActiveUsers: 1100
        },
        recentActivity: [
          {
            type: 'user_registered',
            userEmail: '<EMAIL>',
            timestamp: '2024-01-01T12:00:00Z'
          }
        ]
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockAnalytics
      })

      const response = await fetch('/api/admin/users/analytics', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.totalUsers).toBe(1250)
      expect(result.activeUsers).toBe(1180)
      expect(result.usersByRole.user).toBe(1200)
      expect(result.usersByCompany).toHaveLength(2)
      expect(result.recentActivity).toHaveLength(1)
    })

    it('should get user activity timeline', async () => {
      const mockTimeline = [
        {
          id: 'activity-1',
          type: 'login',
          timestamp: '2024-01-15T10:00:00Z',
          details: { ip: '***********', userAgent: 'Chrome' }
        },
        {
          id: 'activity-2',
          type: 'benefit_ranking_updated',
          timestamp: '2024-01-15T09:30:00Z',
          details: { benefit: 'Health Insurance', newPosition: 1 }
        },
        {
          id: 'activity-3',
          type: 'company_saved',
          timestamp: '2024-01-15T09:00:00Z',
          details: { companyName: 'New Startup' }
        }
      ]

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          timeline: mockTimeline,
          total: 3
        })
      })

      const response = await fetch('/api/admin/users/user-123/activity', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.timeline).toHaveLength(3)
      expect(result.timeline[0].type).toBe('login')
      expect(result.timeline[1].type).toBe('benefit_ranking_updated')
      expect(result.timeline[2].type).toBe('company_saved')
    })

    it('should export user data', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          exportUrl: '/api/admin/exports/users-2024-01-01.csv',
          recordCount: 1250
        })
      })

      const response = await fetch('/api/admin/users/export', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify({ 
          format: 'csv', 
          includeActivity: true,
          dateRange: {
            start: '2024-01-01',
            end: '2024-01-31'
          }
        })
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.exportUrl).toContain('.csv')
      expect(result.recordCount).toBe(1250)
    })
  })

  describe('Authorization and Security', () => {
    it('should require admin authorization', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({ 
          error: 'Authentication required' 
        })
      })

      const response = await fetch('/api/admin/users')

      const result = await response.json()

      expect(response.status).toBe(401)
      expect(result.error).toBe('Authentication required')
    })

    it('should require admin role', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 403,
        json: async () => ({ 
          error: 'Admin access required' 
        })
      })

      const response = await fetch('/api/admin/users', {
        headers: { 
          'Authorization': 'Bearer user-token'
        }
      })

      const result = await response.json()

      expect(response.status).toBe(403)
      expect(result.error).toBe('Admin access required')
    })

    it('should prevent admin from deleting themselves', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ 
          error: 'Cannot delete your own admin account' 
        })
      })

      const response = await fetch('/api/admin/users/current-admin-id', {
        method: 'DELETE',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.error).toBe('Cannot delete your own admin account')
    })
  })
})
