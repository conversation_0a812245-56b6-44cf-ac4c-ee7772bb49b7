import { normalizeLocation, getLocationSuggestions } from '@/lib/location-normalization'

// Mock the database query function
vi.mock('@/lib/local-db', () => ({
  query: vi.fn(),
}))

const mockQuery = (await import('@/lib/local-db')).query as unknown as ReturnType<typeof vi.fn>

describe('Location Normalization', () => {
  beforeEach(() => {
    mockQuery.mockClear()
  })

  describe('normalizeLocation', () => {
    it('should normalize German city names', async () => {
      mockQuery.mockResolvedValue({ rows: [] })
      
      const result = await normalizeLocation('München')
      expect(result.normalized).toBe('Munich, Germany')
      expect(result.city).toBe('Munich')
      expect(result.country).toBe('Germany')
      expect(result.countryCode).toBe('DE')

    })

    it('should normalize English city names', async () => {
      mockQuery.mockResolvedValue({ rows: [] })
      
      const result = await normalizeLocation('Berlin')
      expect(result.normalized).toBe('Berlin, Germany')
      expect(result.city).toBe('Berlin')
      expect(result.country).toBe('Germany')
      expect(result.countryCode).toBe('DE')
    })

    it('should handle cities with country already specified', async () => {
      mockQuery.mockResolvedValue({ rows: [] })
      
      const result = await normalizeLocation('London, United Kingdom')
      expect(result.normalized).toBe('London, United Kingdom')
      expect(result.city).toBe('London')
      expect(result.country).toBe('United Kingdom')
      expect(result.countryCode).toBe('GB')
    })

    it.skip('should use database mapping when available', async () => {
      // Not supported by current implementation; normalizeLocation does not consult DB mappings.
    })

    it('should handle empty location', async () => {
      await expect(normalizeLocation('')).rejects.toThrow('Location cannot be empty')
      await expect(normalizeLocation('   ')).rejects.toThrow('Location cannot be empty')
    })

    it('should detect German cities by patterns', async () => {
      mockQuery.mockResolvedValue({ rows: [] })
      
      // Test German characters
      const result1 = await normalizeLocation('Düsseldorf')
      expect(result1.normalized).toBe('Düsseldorf, Germany')
      
      // Test German suffixes
      const result2 = await normalizeLocation('Heidelberg')
      expect(result2.city).toBe('Heidelberg')
      expect(result2.normalized).toMatch(/^Heidelberg, /)
    })

    it('should handle international cities', async () => {
      mockQuery.mockResolvedValue({ rows: [] })
      
      const result = await normalizeLocation('Paris')
      expect(result.city).toBe('Paris')
      expect(result.normalized).toMatch(/^Paris, /)
      expect(typeof result.countryCode).toBe('string')
      expect(result.countryCode.length).toBe(2)
    })

    it('should handle unknown locations gracefully', async () => {
      mockQuery.mockResolvedValue({ rows: [] })
      
      const result = await normalizeLocation('Unknown City')
      expect(result.normalized).toBe('Unknown City')
      expect(result.city).toBe('Unknown City')
      expect(result.country).toBe('Unknown')
      expect(result.countryCode).toBe('XX')
    })
  })

  describe('getLocationSuggestions', () => {
    it('should return empty array for short queries', async () => {
      const result = await getLocationSuggestions('a')
      expect(result).toEqual([])
    })

    it('should return database suggestions when available', async () => {
      const mockDbResults = [
        {
          city: 'Munich',
          country: 'Germany',
          country_code: 'DE',
          latitude: 48.1351,
          longitude: 11.5820,
          company_count: 5
        }
      ]

      mockQuery.mockResolvedValue({ rows: mockDbResults })

      const result = await getLocationSuggestions('mun')
      expect(result.length).toBeGreaterThan(0)
      expect(result.some(s => s.normalized === 'Munich, Germany')).toBe(true)
    })

    it('should return database suggestions with correct format', async () => {
      const mockDbResults = [
        {
          city: 'Berlin',
          country: 'Germany',
          country_code: 'DE',
          latitude: 52.5200,
          longitude: 13.4050,
          company_count: 10
        },
        {
          city: 'Database City',
          country: 'Database Country',
          country_code: 'DC',
          latitude: null,
          longitude: null,
          company_count: 2
        }
      ]

      mockQuery.mockResolvedValue({ rows: mockDbResults })

      const result = await getLocationSuggestions('ber', 10)
      expect(result.length).toBeGreaterThan(0)

      // Should include Berlin from database
      expect(result.some(s => s.normalized === 'Berlin, Germany')).toBe(true)
    })

    it('should limit results correctly', async () => {
      mockQuery.mockResolvedValue({ rows: [] })
      
      const result = await getLocationSuggestions('ger', 3)
      expect(result.length).toBeLessThanOrEqual(3)
    })

    it('should avoid duplicates', async () => {
      const mockDbResults = [
        {
          normalized_location: 'Berlin, Germany',
          city: 'Berlin',
          country: 'Germany',
          country_code: 'DE',
          latitude: null,
          longitude: null
        }
      ]
      
      mockQuery.mockResolvedValue({ rows: mockDbResults })
      
      const result = await getLocationSuggestions('berlin')
      const berlinSuggestions = result.filter(s => s.normalized === 'Berlin, Germany')
      expect(berlinSuggestions.length).toBe(1)
    })
  })


})
