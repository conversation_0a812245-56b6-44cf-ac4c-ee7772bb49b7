/**
 * API Integration Tests
 * Tests for all REST API endpoints with authentication, authorization, and error handling
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock fetch for API calls
global.fetch = vi.fn()

// Mock Next.js environment
vi.mock('next/headers', () => ({
  cookies: () => ({
    get: vi.fn(),
    set: vi.fn(),
    delete: vi.fn(),
  }),
}))

// Mock database operations
vi.mock('@/lib/local-db', () => ({
  query: vi.fn(),
}))

const mockQuery = (await import('@/lib/local-db')).query as unknown as ReturnType<typeof vi.fn>

describe('API Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(fetch as any).mockClear()
    mockQuery.mockReset()
  })

  describe('Authentication Endpoints', () => {
    describe('POST /api/auth/sign-up', () => {
      it('should create new user account', async () => {
        const userData = {
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User'
        }

        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 201,
          json: async () => ({ 
            success: true, 
            message: 'Magic link sent to your email'
          })
        })

        const response = await fetch('/api/auth/sign-up', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(userData)
        })

        expect(response.status).toBe(201)
        const result = await response.json()
        expect(result.success).toBe(true)
      })

      it('should validate email format', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: false,
          status: 400,
          json: async () => ({ 
            error: 'Invalid email format' 
          })
        })

        const response = await fetch('/api/auth/sign-up', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email: 'invalid-email' })
        })

        expect(response.status).toBe(400)
      })
    })

    describe('POST /api/auth/sign-in', () => {
      it('should send magic link for existing user', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => ({ 
            success: true, 
            message: 'Magic link sent'
          })
        })

        const response = await fetch('/api/auth/sign-in', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email: '<EMAIL>' })
        })

        expect(response.status).toBe(200)
      })

      it('should handle non-existent user', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: false,
          status: 404,
          json: async () => ({ 
            error: 'User not found' 
          })
        })

        const response = await fetch('/api/auth/sign-in', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email: '<EMAIL>' })
        })

        expect(response.status).toBe(404)
      })
    })

    describe('POST /api/auth/magic-link', () => {
      it('should verify valid magic link', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => ({ 
            success: true, 
            user: { id: 'user-123', email: '<EMAIL>' }
          })
        })

        const response = await fetch('/api/auth/magic-link', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ token: 'valid-token' })
        })

        expect(response.status).toBe(200)
      })

      it('should reject expired token', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: false,
          status: 401,
          json: async () => ({ 
            error: 'Token expired' 
          })
        })

        const response = await fetch('/api/auth/magic-link', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ token: 'expired-token' })
        })

        expect(response.status).toBe(401)
      })
    })
  })

  describe('Company Endpoints', () => {
    describe('GET /api/companies', () => {
      it('should return paginated companies', async () => {
        const mockCompanies = {
          companies: [
            { id: 'company-1', name: 'Tech Corp' },
            { id: 'company-2', name: 'Design Studio' }
          ],
          total: 2,
          page: 1,
          limit: 20
        }

        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => mockCompanies
        })

        const response = await fetch('/api/companies?page=1&limit=20')

        expect(response.status).toBe(200)
        const result = await response.json()
        expect(result.companies).toHaveLength(2)
      })

      it('should filter companies by search query', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => ({ 
            companies: [{ id: 'company-1', name: 'Tech Corp' }],
            total: 1
          })
        })

        const response = await fetch('/api/companies?search=tech')

        expect(response.status).toBe(200)
      })
    })

    describe('GET /api/companies/[id]', () => {
      it('should return company details', async () => {
        const mockCompany = {
          id: 'company-123',
          name: 'Tech Corp',
          benefits: [
            { id: 'benefit-1', name: 'Health Insurance' }
          ]
        }

        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => mockCompany
        })

        const response = await fetch('/api/companies/company-123')

        expect(response.status).toBe(200)
        const result = await response.json()
        expect(result.id).toBe('company-123')
      })

      it('should handle non-existent company', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: false,
          status: 404,
          json: async () => ({ 
            error: 'Company not found' 
          })
        })

        const response = await fetch('/api/companies/nonexistent')

        expect(response.status).toBe(404)
      })
    })

    describe('POST /api/companies/[id]/benefits', () => {
      it('should add benefit to company (authenticated)', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 201,
          json: async () => ({ 
            success: true, 
            companyBenefit: { id: 'cb-123' }
          })
        })

        const response = await fetch('/api/companies/company-123/benefits', {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'Authorization': 'Bearer user-token'
          },
          body: JSON.stringify({ benefitId: 'benefit-456' })
        })

        expect(response.status).toBe(201)
      })

      it('should require authentication', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: false,
          status: 401,
          json: async () => ({ 
            error: 'Authentication required' 
          })
        })

        const response = await fetch('/api/companies/company-123/benefits', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ benefitId: 'benefit-456' })
        })

        expect(response.status).toBe(401)
      })
    })
  })

  describe('Benefit Endpoints', () => {
    describe('GET /api/benefits', () => {
      it('should return all benefits', async () => {
        const mockBenefits = {
          benefits: [
            { id: 'benefit-1', name: 'Health Insurance', category: 'Health' },
            { id: 'benefit-2', name: 'Remote Work', category: 'Flexibility' }
          ]
        }

        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => mockBenefits
        })

        const response = await fetch('/api/benefits')

        expect(response.status).toBe(200)
        const result = await response.json()
        expect(result.benefits).toHaveLength(2)
      })

      it('should filter benefits by category', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => ({ 
            benefits: [{ id: 'benefit-1', name: 'Health Insurance', category: 'Health' }]
          })
        })

        const response = await fetch('/api/benefits?category=Health')

        expect(response.status).toBe(200)
      })
    })

    describe('GET /api/benefits/[id]', () => {
      it('should return benefit details', async () => {
        const mockBenefit = {
          id: 'benefit-123',
          name: 'Health Insurance',
          description: 'Comprehensive health coverage',
          category: 'Health'
        }

        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => mockBenefit
        })

        const response = await fetch('/api/benefits/benefit-123')

        expect(response.status).toBe(200)
        const result = await response.json()
        expect(result.id).toBe('benefit-123')
      })
    })
  })

  describe('User Endpoints', () => {
    describe('GET /api/user/profile', () => {
      it('should return user profile (authenticated)', async () => {
        const mockProfile = {
          id: 'user-123',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User'
        }

        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => mockProfile
        })

        const response = await fetch('/api/user/profile', {
          headers: { 'Authorization': 'Bearer user-token' }
        })

        expect(response.status).toBe(200)
        const result = await response.json()
        expect(result.id).toBe('user-123')
      })

      it('should require authentication', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: false,
          status: 401,
          json: async () => ({ 
            error: 'Authentication required' 
          })
        })

        const response = await fetch('/api/user/profile')

        expect(response.status).toBe(401)
      })
    })

    describe('POST /api/user/benefit-ranking', () => {
      it('should add benefit to ranking (authenticated)', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 201,
          json: async () => ({ 
            success: true, 
            ranking: { id: 'ranking-123', position: 1 }
          })
        })

        const response = await fetch('/api/user/benefit-ranking', {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'Authorization': 'Bearer user-token'
          },
          body: JSON.stringify({ benefitId: 'benefit-123', position: 1 })
        })

        expect(response.status).toBe(201)
      })
    })

    describe('GET /api/user/saved-companies', () => {
      it('should return saved companies (authenticated)', async () => {
        const mockSavedCompanies = {
          savedCompanies: [
            { id: 'saved-1', company: { name: 'Tech Corp' } }
          ]
        }

        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => mockSavedCompanies
        })

        const response = await fetch('/api/user/saved-companies', {
          headers: { 'Authorization': 'Bearer user-token' }
        })

        expect(response.status).toBe(200)
      })
    })
  })

  describe('Admin Endpoints', () => {
    describe('GET /api/admin/companies', () => {
      it('should return companies for admin', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => ({ 
            companies: [],
            total: 0
          })
        })

        const response = await fetch('/api/admin/companies', {
          headers: { 'Authorization': 'Bearer admin-token' }
        })

        expect(response.status).toBe(200)
      })

      it('should require admin role', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: false,
          status: 403,
          json: async () => ({ 
            error: 'Admin access required' 
          })
        })

        const response = await fetch('/api/admin/companies', {
          headers: { 'Authorization': 'Bearer user-token' }
        })

        expect(response.status).toBe(403)
      })
    })

    describe('POST /api/admin/companies', () => {
      it('should create company (admin)', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 201,
          json: async () => ({ 
            success: true, 
            company: { id: 'company-123', name: 'New Corp' }
          })
        })

        const response = await fetch('/api/admin/companies', {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'Authorization': 'Bearer admin-token'
          },
          body: JSON.stringify({ name: 'New Corp' })
        })

        expect(response.status).toBe(201)
      })
    })

    describe('DELETE /api/admin/companies/[id]', () => {
      it('should delete company (admin)', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => ({ 
            success: true, 
            message: 'Company deleted'
          })
        })

        const response = await fetch('/api/admin/companies/company-123', {
          method: 'DELETE',
          headers: { 'Authorization': 'Bearer admin-token' }
        })

        expect(response.status).toBe(200)
      })
    })

    describe('POST /api/admin/analytics/reset', () => {
      it('should reset analytics (super admin)', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => ({ 
            success: true, 
            message: 'Analytics reset'
          })
        })

        const response = await fetch('/api/admin/analytics/reset?type=all', {
          method: 'POST',
          headers: { 'Authorization': 'Bearer super-admin-token' }
        })

        expect(response.status).toBe(200)
      })

      it('should require super admin role', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: false,
          status: 403,
          json: async () => ({ 
            error: 'Super admin access required' 
          })
        })

        const response = await fetch('/api/admin/analytics/reset?type=all', {
          method: 'POST',
          headers: { 'Authorization': 'Bearer admin-token' }
        })

        expect(response.status).toBe(403)
      })
    })
  })

  describe('Analytics Endpoints', () => {
    describe('POST /api/analytics/track', () => {
      it('should track analytics event', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => ({ 
            success: true 
          })
        })

        const response = await fetch('/api/analytics/track', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'page_view',
            data: { page: '/companies' }
          })
        })

        expect(response.status).toBe(200)
      })

      it('should validate tracking data', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: false,
          status: 400,
          json: async () => ({ 
            error: 'Invalid tracking data' 
          })
        })

        const response = await fetch('/api/analytics/track', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ invalid: 'data' })
        })

        expect(response.status).toBe(400)
      })
    })

    describe('GET /api/analytics/insights', () => {
      it('should return insights for premium users', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => ({ 
            isPremium: true,
            insights: { totalViews: 1000 }
          })
        })

        const response = await fetch('/api/analytics/insights', {
          headers: { 'Authorization': 'Bearer premium-user-token' }
        })

        expect(response.status).toBe(200)
      })

      it('should return preview for non-premium users', async () => {
        ;(fetch as any).mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => ({ 
            isPremium: false,
            preview: true,
            upgradePrompt: 'Upgrade for full insights'
          })
        })

        const response = await fetch('/api/analytics/insights', {
          headers: { 'Authorization': 'Bearer user-token' }
        })

        expect(response.status).toBe(200)
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle 404 for non-existent endpoints', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ 
          error: 'Endpoint not found' 
        })
      })

      const response = await fetch('/api/nonexistent-endpoint')

      expect(response.status).toBe(404)
    })

    it('should handle 405 for unsupported methods', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 405,
        json: async () => ({ 
          error: 'Method not allowed' 
        })
      })

      const response = await fetch('/api/companies', {
        method: 'PATCH'
      })

      expect(response.status).toBe(405)
    })

    it('should handle 500 for server errors', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({ 
          error: 'Internal server error' 
        })
      })

      const response = await fetch('/api/companies')

      expect(response.status).toBe(500)
    })

    it('should handle rate limiting', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 429,
        json: async () => ({ 
          error: 'Rate limit exceeded',
          retryAfter: 60
        })
      })

      const response = await fetch('/api/analytics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'test' })
      })

      expect(response.status).toBe(429)
    })
  })
})
