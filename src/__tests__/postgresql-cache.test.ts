// Tests for PostgreSQL cache system

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import {
  setCache,
  getCache,
  deleteCache,
  clearCachePattern,
  setSession,
  getSession,
  deleteSession,
  setCSRFToken,
  getCSRFToken,
  checkCacheHealth,
  getCacheStats
} from '../lib/postgresql-cache'

// Mock the database
vi.mock('../lib/local-db', () => ({
  query: vi.fn(),
}))

const mockQuery = (await import('../lib/local-db')).query as unknown as ReturnType<typeof vi.fn>

describe('PostgreSQL Cache System', () => {
  beforeEach(() => {
    mockQuery.mockReset()
  })

  describe('Basic Cache Operations', () => {
    it('should set cache successfully', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [{ set_cache: true }] })
      
      const result = await setCache('test_key', { data: 'test' }, 3600)
      
      expect(result).toBe(true)
      expect(mockQuery).toHaveBeenCalledWith(
        'SELECT set_cache($1, $2, $3)',
        ['test_key', '{"data":"test"}', 3600]
      )
    })

    it('should get cache successfully', async () => {
      const testData = { data: 'test' }
      // PostgreSQL JSONB is already parsed by the pg library, so mock it as an object
      mockQuery.mockResolvedValueOnce({ rows: [{ get_cache: testData }] })

      const result = await getCache('test_key')

      expect(result).toEqual(testData)
      expect(mockQuery).toHaveBeenCalledWith('SELECT get_cache($1)', ['test_key'])
    })

    it('should return null for non-existent cache', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [{ get_cache: null }] })
      
      const result = await getCache('non_existent_key')
      
      expect(result).toBeNull()
    })

    it('should delete cache successfully', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [{ delete_cache: true }] })
      
      const result = await deleteCache('test_key')
      
      expect(result).toBe(true)
      expect(mockQuery).toHaveBeenCalledWith('SELECT delete_cache($1)', ['test_key'])
    })

    it('should clear cache pattern successfully', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [{ clear_cache_pattern: 5 }] })
      
      const result = await clearCachePattern('test_*')
      
      expect(result).toBe(5)
      expect(mockQuery).toHaveBeenCalledWith('SELECT clear_cache_pattern($1)', ['test_*'])
    })
  })

  describe('Session Management', () => {
    it('should set session successfully', async () => {
      // Mock the session config query first (called by setSession)
      mockQuery.mockResolvedValueOnce({ rows: [] }) // No max sessions config
      // Mock the main session insert query
      mockQuery.mockResolvedValueOnce({ rows: [{ id: 'session-id' }] })
      // Mock the session activity logging query
      mockQuery.mockResolvedValueOnce({ rows: [] })

      const expiresAt = new Date(Date.now() + 3600000)
      const result = await setSession('session_token', 'user_id', expiresAt)

      expect(result).toBe(true)
      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO user_sessions'),
        ['user_id', 'session_token', expiresAt]
      )
    })

    it('should get session successfully', async () => {
      const sessionData = {
        user_id: 'user_id',
        expires_at: new Date(),
        created_at: new Date()
      }
      // Mock the main session query
      mockQuery.mockResolvedValueOnce({ rows: [sessionData] })
      // Mock the session activity logging query
      mockQuery.mockResolvedValueOnce({ rows: [] })
      // Mock the session config query for extension
      mockQuery.mockResolvedValueOnce({ rows: [] })

      const result = await getSession('session_token')

      expect(result).toEqual({
        userId: sessionData.user_id,
        expiresAt: sessionData.expires_at.toISOString(),
        createdAt: sessionData.created_at.toISOString()
      })
    })

    it('should delete session successfully', async () => {
      // Mock the session activity logging query first (called before deletion)
      mockQuery.mockResolvedValueOnce({ rows: [] })
      // Mock the delete query
      mockQuery.mockResolvedValueOnce({ rowCount: 1 })

      const result = await deleteSession('session_token')

      expect(result).toBe(true)
      expect(mockQuery).toHaveBeenCalledWith(
        'DELETE FROM user_sessions WHERE session_token = $1',
        ['session_token']
      )
    })
  })

  describe('CSRF Token Management', () => {
    it('should set CSRF token successfully', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [{ set_csrf_token: true }] })
      
      const result = await setCSRFToken('session_id', 'csrf_token', 3600)
      
      expect(result).toBe(true)
      expect(mockQuery).toHaveBeenCalledWith(
        'SELECT set_csrf_token($1, $2, $3)',
        ['session_id', 'csrf_token', 3600]
      )
    })

    it('should get CSRF token successfully', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [{ get_csrf_token: 'csrf_token' }] })
      
      const result = await getCSRFToken('session_id')
      
      expect(result).toBe('csrf_token')
      expect(mockQuery).toHaveBeenCalledWith('SELECT get_csrf_token($1)', ['session_id'])
    })
  })

  describe('Health and Stats', () => {
    it('should check cache health successfully', async () => {
      // Mock Date.now to return a consistent timestamp
      const mockTimestamp = **********
      const originalDateNow = Date.now
      Date.now = vi.fn(() => mockTimestamp)

      // Mock successful cache operations
      mockQuery
        .mockResolvedValueOnce({ rows: [{ set_cache: true }] }) // setCache
        .mockResolvedValueOnce({ rows: [{ get_cache: { timestamp: mockTimestamp } }] }) // getCache (JSONB already parsed)
        .mockResolvedValueOnce({ rows: [{ delete_cache: true }] }) // deleteCache
        .mockResolvedValueOnce({ rows: [{ test: 1 }] }) // connectivity test

      const result = await checkCacheHealth()

      expect(result).toBe(true)

      // Restore Date.now
      Date.now = originalDateNow
    })

    it('should get cache stats successfully', async () => {
      const statsData = {
        total_entries: 100,
        expired_entries: 5,
        oldest_entry: new Date(),
        newest_entry: new Date()
      }
      mockQuery.mockResolvedValueOnce({ rows: [statsData] })
      
      const result = await getCacheStats()
      
      expect(result).toEqual({
        totalCacheEntries: 100,
        expiredEntries: 5,
        oldestEntry: statsData.oldest_entry.toISOString(),
        newestEntry: statsData.newest_entry.toISOString()
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle cache set errors gracefully', async () => {
      mockQuery.mockRejectedValueOnce(new Error('Database error'))
      
      const result = await setCache('test_key', { data: 'test' })
      
      expect(result).toBe(false)
    })

    it('should handle cache get errors gracefully', async () => {
      mockQuery.mockRejectedValueOnce(new Error('Database error'))
      
      const result = await getCache('test_key')
      
      expect(result).toBeNull()
    })

    it('should handle health check errors gracefully', async () => {
      mockQuery.mockRejectedValueOnce(new Error('Database error'))
      
      const result = await checkCacheHealth()
      
      expect(result).toBe(false)
    })
  })
})
