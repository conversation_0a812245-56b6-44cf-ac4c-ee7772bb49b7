/**
 * Analytics System Tests
 * Tests for the analytics tracking system, API endpoints, and data integrity
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'

// Mock fetch for API calls
global.fetch = vi.fn()

// Test configuration
const BASE_URL = 'http://localhost:3000'
const TEST_TIMEOUT = 30000

// Test data
const TEST_COMPANY_ID = 'test-company-123'
const TEST_BENEFIT_ID = 'test-benefit-456'
const TEST_USER_ID = 'test-user-789'

// Helper functions
async function makeRequest(endpoint: string, options: RequestInit = {}) {
  const url = `${BASE_URL}${endpoint}`
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  })
  
  const data = await response.json()
  return { response, data }
}

// Mock database operations
vi.mock('@/lib/local-db', () => ({
  query: vi.fn(),
}))

const mockQuery = (await import('@/lib/local-db')).query as unknown as ReturnType<typeof vi.fn>

describe('Analytics System', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset fetch mock
    ;(fetch as any).mockClear()
  })

  describe('Analytics Tracking', () => {
    it('should track company views', async () => {
      // Mock successful tracking response
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })

      const response = await makeRequest('/api/analytics/track', {
        method: 'POST',
        body: JSON.stringify({
          type: 'company_view',
          data: {
            companyId: TEST_COMPANY_ID,
            referrer: 'https://example.com'
          }
        })
      })

      expect(fetch).toHaveBeenCalledWith(
        `${BASE_URL}/api/analytics/track`,
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          }),
          body: JSON.stringify({
            type: 'company_view',
            data: {
              companyId: TEST_COMPANY_ID,
              referrer: 'https://example.com'
            }
          })
        })
      )
    })

    it('should track benefit searches', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })

      const response = await makeRequest('/api/analytics/track', {
        method: 'POST',
        body: JSON.stringify({
          type: 'benefit_search',
          data: {
            query: 'health insurance',
            results_count: 5
          }
        })
      })

      expect(fetch).toHaveBeenCalledWith(
        `${BASE_URL}/api/analytics/track`,
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            type: 'benefit_search',
            data: {
              query: 'health insurance',
              results_count: 5
            }
          })
        })
      )
    })

    it('should track user interactions', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })

      const response = await makeRequest('/api/analytics/track', {
        method: 'POST',
        body: JSON.stringify({
          type: 'user_interaction',
          data: {
            action: 'save_company',
            companyId: TEST_COMPANY_ID,
            userId: TEST_USER_ID
          }
        })
      })

      expect(fetch).toHaveBeenCalledWith(
        `${BASE_URL}/api/analytics/track`,
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            type: 'user_interaction',
            data: {
              action: 'save_company',
              companyId: TEST_COMPANY_ID,
              userId: TEST_USER_ID
            }
          })
        })
      )
    })
  })

  describe('Analytics Data Retrieval', () => {
    it('should retrieve analytics insights for paying users', async () => {
      const mockInsights = {
        totalViews: 1000,
        topBenefits: ['Health Insurance', 'Remote Work'],
        userEngagement: 85
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockInsights
      })

      const response = await makeRequest('/api/analytics/insights')

      expect(response.data).toEqual(mockInsights)
    })

    it('should return preview data for non-paying users', async () => {
      const mockPreviewData = {
        preview: true,
        message: 'Upgrade to see full analytics'
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockPreviewData
      })

      const response = await makeRequest('/api/analytics/insights?preview=true')

      expect(response.data).toEqual(mockPreviewData)
    })
  })

  describe('Admin Analytics', () => {
    it('should allow admin to reset analytics data', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, message: 'Analytics data reset' })
      })

      const response = await makeRequest('/api/admin/analytics/reset?type=all', {
        method: 'POST'
      })

      expect(response.data.success).toBe(true)
    })

    it('should provide admin analytics dashboard data', async () => {
      const mockDashboardData = {
        totalUsers: 500,
        totalCompanies: 100,
        totalBenefits: 50,
        dailyActiveUsers: 25
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockDashboardData
      })

      const response = await makeRequest('/api/admin/analytics/dashboard')

      expect(response.data).toEqual(mockDashboardData)
    })
  })

  describe('Error Handling', () => {
    it('should handle invalid tracking data', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ error: 'Invalid tracking data' })
      })

      const response = await makeRequest('/api/analytics/track', {
        method: 'POST',
        body: JSON.stringify({
          type: 'invalid_type',
          data: {}
        })
      })

      expect(response.response.ok).toBe(false)
      expect(response.data.error).toBe('Invalid tracking data')
    })

    it('should handle unauthorized access to admin endpoints', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({ error: 'Unauthorized' })
      })

      const response = await makeRequest('/api/admin/analytics/reset', {
        method: 'POST'
      })

      expect(response.response.ok).toBe(false)
      expect(response.data.error).toBe('Unauthorized')
    })
  })
})
