/**
 * Simple Analytics System Tests
 * Tests analytics tracking without authentication requirements
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock fetch for API calls
global.fetch = vi.fn()

// Test configuration
const BASE_URL = 'http://localhost:3000'

// Helper functions
async function makeRequest(endpoint: string, options: RequestInit = {}) {
  const url = `${BASE_URL}${endpoint}`
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  })
  
  const data = await response.json()
  return { response, data }
}

// Mock database operations
vi.mock('@/lib/local-db', () => ({
  query: vi.fn(),
}))

const mockQuery = (await import('@/lib/local-db')).query as unknown as ReturnType<typeof vi.fn>

describe('Simple Analytics System', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(fetch as any).mockClear()
  })

  describe('Basic Analytics Tracking', () => {
    it('should track page views without authentication', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })

      const response = await makeRequest('/api/analytics/track', {
        method: 'POST',
        body: JSON.stringify({
          type: 'page_view',
          data: {
            page: '/companies',
            referrer: 'https://google.com'
          }
        })
      })

      expect(fetch).toHaveBeenCalledWith(
        `${BASE_URL}/api/analytics/track`,
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            type: 'page_view',
            data: {
              page: '/companies',
              referrer: 'https://google.com'
            }
          })
        })
      )
    })

    it('should track search queries', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })

      const response = await makeRequest('/api/analytics/track', {
        method: 'POST',
        body: JSON.stringify({
          type: 'search_query',
          data: {
            query: 'remote work',
            category: 'benefits',
            results_count: 12
          }
        })
      })

      expect(response.response.ok).toBe(true)
      expect(response.data.success).toBe(true)
    })

    it('should track company profile views', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })

      const companyId = 'test-company-123'
      const response = await makeRequest('/api/analytics/track', {
        method: 'POST',
        body: JSON.stringify({
          type: 'company_profile_view',
          data: {
            companyId,
            source: 'search_results'
          }
        })
      })

      expect(response.response.ok).toBe(true)
    })
  })

  describe('Analytics Data Validation', () => {
    it('should validate required fields in tracking data', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ error: 'Missing required field: type' })
      })

      const response = await makeRequest('/api/analytics/track', {
        method: 'POST',
        body: JSON.stringify({
          data: {
            page: '/companies'
          }
        })
      })

      expect(response.response.ok).toBe(false)
      expect(response.data.error).toContain('Missing required field')
    })

    it('should validate tracking data format', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ error: 'Invalid data format' })
      })

      const response = await makeRequest('/api/analytics/track', {
        method: 'POST',
        body: JSON.stringify({
          type: 'page_view',
          data: 'invalid_data_format'
        })
      })

      expect(response.response.ok).toBe(false)
    })
  })

  describe('Rate Limiting', () => {
    it('should handle rate limiting for analytics tracking', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 429,
        json: async () => ({ error: 'Rate limit exceeded' })
      })

      const response = await makeRequest('/api/analytics/track', {
        method: 'POST',
        body: JSON.stringify({
          type: 'page_view',
          data: { page: '/test' }
        })
      })

      expect(response.response.status).toBe(429)
      expect(response.data.error).toBe('Rate limit exceeded')
    })
  })

  describe('Analytics Aggregation', () => {
    it('should aggregate daily analytics data', async () => {
      // Mock database query for aggregation
      mockQuery.mockResolvedValueOnce({
        rows: [
          {
            date: '2024-01-01',
            total_views: 100,
            unique_visitors: 50,
            top_pages: ['/companies', '/benefits']
          }
        ]
      })

      const mockAggregatedData = {
        date: '2024-01-01',
        totalViews: 100,
        uniqueVisitors: 50,
        topPages: ['/companies', '/benefits']
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockAggregatedData
      })

      const response = await makeRequest('/api/analytics/daily-summary?date=2024-01-01')

      expect(response.data).toEqual(mockAggregatedData)
    })

    it('should provide weekly analytics trends', async () => {
      const mockTrendData = {
        week: '2024-W01',
        totalViews: 700,
        growth: 15.5,
        topBenefits: ['Health Insurance', 'Remote Work', 'Flexible Hours']
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockTrendData
      })

      const response = await makeRequest('/api/analytics/weekly-trends?week=2024-W01')

      expect(response.data).toEqual(mockTrendData)
    })
  })

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      ;(fetch as any).mockRejectedValueOnce(new Error('Network error'))

      try {
        await makeRequest('/api/analytics/track', {
          method: 'POST',
          body: JSON.stringify({
            type: 'page_view',
            data: { page: '/test' }
          })
        })
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
        expect((error as Error).message).toBe('Network error')
      }
    })

    it('should handle malformed JSON responses', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => {
          throw new Error('Invalid JSON')
        }
      })

      try {
        await makeRequest('/api/analytics/track', {
          method: 'POST',
          body: JSON.stringify({
            type: 'page_view',
            data: { page: '/test' }
          })
        })
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
      }
    })
  })
})
