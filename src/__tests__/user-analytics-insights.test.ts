/**
 * User Analytics and Insights Flow Tests
 * Tests for premium analytics insights, non-premium preview data, and user analytics interactions
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock fetch for API calls
global.fetch = vi.fn()

// Mock database operations
vi.mock('@/lib/local-db', () => ({
  query: vi.fn(),
}))

const mockQuery = (await import('@/lib/local-db')).query as unknown as ReturnType<typeof vi.fn>

describe('User Analytics and Insights Flows', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(fetch as any).mockClear()
    mockQuery.mockReset()
  })

  describe('Premium Analytics Insights', () => {
    it('should provide full analytics insights for premium users', async () => {
      const mockPremiumInsights = {
        isPremium: true,
        personalInsights: {
          benefitRankingTrends: [
            { benefit: 'Health Insurance', trend: 'stable', position: 1 },
            { benefit: 'Remote Work', trend: 'rising', position: 2 }
          ],
          companyMatchScore: 85,
          recommendedCompanies: [
            {
              id: 'company-1',
              name: 'Tech Corp',
              matchScore: 92,
              matchReasons: ['Health Insurance', 'Remote Work', 'Flexible Hours']
            }
          ]
        },
        marketInsights: {
          topBenefitTrends: [
            { benefit: 'Remote Work', growth: 25.5, rank: 1 },
            { benefit: 'Mental Health Support', growth: 18.2, rank: 2 }
          ],
          industryBenchmarks: {
            technology: {
              averageBenefits: 12,
              topBenefits: ['Health Insurance', 'Remote Work', 'Stock Options']
            }
          },
          salaryInsights: {
            averageByLocation: {
              'San Francisco, CA': 150000,
              'New York, NY': 140000
            }
          }
        },
        competitiveAnalysis: {
          similarCompanies: [
            {
              id: 'company-2',
              name: 'Competitor Corp',
              benefitOverlap: 80,
              uniqueBenefits: ['Sabbatical Leave']
            }
          ]
        }
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockPremiumInsights
      })

      const response = await fetch('/api/user/analytics/insights')

      const result = await response.json()

      expect(result.isPremium).toBe(true)
      expect(result.personalInsights.companyMatchScore).toBe(85)
      expect(result.marketInsights.topBenefitTrends).toHaveLength(2)
      expect(result.competitiveAnalysis.similarCompanies).toHaveLength(1)
    })

    it('should provide detailed benefit ranking analytics for premium users', async () => {
      const mockBenefitAnalytics = {
        isPremium: true,
        benefitAnalytics: {
          personalRanking: [
            {
              benefit: 'Health Insurance',
              position: 1,
              stabilityScore: 95,
              marketDemand: 'high',
              salaryImpact: '+$5000'
            },
            {
              benefit: 'Remote Work',
              position: 2,
              stabilityScore: 88,
              marketDemand: 'very high',
              salaryImpact: '+$8000'
            }
          ],
          industryComparison: {
            yourRanking: ['Health Insurance', 'Remote Work', 'Flexible Hours'],
            industryAverage: ['Health Insurance', 'Retirement Plan', 'Remote Work'],
            uniquePreferences: ['Flexible Hours']
          },
          recommendations: [
            {
              action: 'consider',
              benefit: 'Mental Health Support',
              reason: 'Growing trend in your industry',
              impact: 'high'
            }
          ]
        }
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockBenefitAnalytics
      })

      const response = await fetch('/api/user/analytics/benefit-ranking')

      const result = await response.json()

      expect(result.isPremium).toBe(true)
      expect(result.benefitAnalytics.personalRanking).toHaveLength(2)
      expect(result.benefitAnalytics.recommendations).toHaveLength(1)
    })

    it('should provide company comparison analytics for premium users', async () => {
      const mockComparisonAnalytics = {
        isPremium: true,
        companyComparison: {
          savedCompanies: [
            {
              id: 'company-1',
              name: 'Tech Corp',
              benefitScore: 85,
              benefitMatch: 12,
              totalBenefits: 15,
              strengths: ['Health Insurance', 'Remote Work'],
              gaps: ['Sabbatical Leave']
            },
            {
              id: 'company-2',
              name: 'Design Studio',
              benefitScore: 72,
              benefitMatch: 8,
              totalBenefits: 12,
              strengths: ['Creative Freedom', 'Flexible Hours'],
              gaps: ['Health Insurance', 'Retirement Plan']
            }
          ],
          recommendation: {
            topChoice: 'company-1',
            reason: 'Best benefit match for your preferences'
          }
        }
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockComparisonAnalytics
      })

      const response = await fetch('/api/user/analytics/company-comparison')

      const result = await response.json()

      expect(result.isPremium).toBe(true)
      expect(result.companyComparison.savedCompanies).toHaveLength(2)
      expect(result.companyComparison.recommendation.topChoice).toBe('company-1')
    })
  })

  describe('Non-Premium Preview Data', () => {
    it('should provide limited preview data for non-premium users', async () => {
      const mockPreviewData = {
        isPremium: false,
        preview: true,
        limitedInsights: {
          benefitRankingPreview: [
            { benefit: 'Health Insurance', position: 1 },
            { benefit: 'Remote Work', position: 2 }
          ],
          topTrend: {
            benefit: 'Remote Work',
            growth: 'High growth trend'
          },
          companyMatchPreview: {
            topMatch: 'Tech Corp',
            score: 'High match'
          }
        },
        upgradePrompt: {
          message: 'Upgrade to see detailed analytics and personalized insights',
          features: [
            'Detailed benefit trend analysis',
            'Personalized company recommendations',
            'Industry salary benchmarks',
            'Competitive analysis'
          ]
        }
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockPreviewData
      })

      const response = await fetch('/api/user/analytics/insights')

      const result = await response.json()

      expect(result.isPremium).toBe(false)
      expect(result.preview).toBe(true)
      expect(result.limitedInsights.benefitRankingPreview).toHaveLength(2)
      expect(result.upgradePrompt.features).toHaveLength(4)
    })

    it('should show upgrade prompt for detailed analytics', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 402,
        json: async () => ({ 
          error: 'Premium subscription required',
          upgradeUrl: '/upgrade'
        })
      })

      const response = await fetch('/api/user/analytics/detailed-insights')

      const result = await response.json()

      expect(response.status).toBe(402)
      expect(result.error).toBe('Premium subscription required')
      expect(result.upgradeUrl).toBe('/upgrade')
    })

    it('should limit data export for non-premium users', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 402,
        json: async () => ({ 
          error: 'Data export requires premium subscription'
        })
      })

      const response = await fetch('/api/user/analytics/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ format: 'csv' })
      })

      const result = await response.json()

      expect(response.status).toBe(402)
      expect(result.error).toBe('Data export requires premium subscription')
    })
  })

  describe('Analytics Interaction Tracking', () => {
    it('should track analytics page views', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true 
        })
      })

      const response = await fetch('/api/analytics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'analytics_page_view',
          data: {
            page: 'insights',
            isPremium: true
          }
        })
      })

      const result = await response.json()

      expect(result.success).toBe(true)
    })

    it('should track insight interactions', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true 
        })
      })

      const response = await fetch('/api/analytics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'insight_interaction',
          data: {
            insightType: 'benefit_trend',
            action: 'view_details',
            benefit: 'Remote Work'
          }
        })
      })

      const result = await response.json()

      expect(result.success).toBe(true)
    })

    it('should track upgrade prompt interactions', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true 
        })
      })

      const response = await fetch('/api/analytics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'upgrade_prompt_interaction',
          data: {
            action: 'clicked',
            source: 'analytics_insights',
            feature: 'detailed_trends'
          }
        })
      })

      const result = await response.json()

      expect(result.success).toBe(true)
    })
  })

  describe('Analytics Data Refresh', () => {
    it('should refresh user analytics data', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true,
          message: 'Analytics data refreshed',
          lastUpdated: new Date().toISOString()
        })
      })

      const response = await fetch('/api/user/analytics/refresh', {
        method: 'POST'
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.message).toBe('Analytics data refreshed')
      expect(result.lastUpdated).toBeDefined()
    })

    it('should handle analytics refresh rate limiting', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 429,
        json: async () => ({ 
          error: 'Analytics refresh rate limit exceeded',
          retryAfter: 300
        })
      })

      const response = await fetch('/api/user/analytics/refresh', {
        method: 'POST'
      })

      const result = await response.json()

      expect(response.status).toBe(429)
      expect(result.error).toBe('Analytics refresh rate limit exceeded')
      expect(result.retryAfter).toBe(300)
    })
  })
})
