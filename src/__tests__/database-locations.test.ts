import {
  getCompanyLocations,
  addCompanyLocation,
  updateCompanyLocation,
  removeCompanyLocation
} from '@/lib/database'

// Mock the database and location normalization
vi.mock('@/lib/local-db', () => ({
  query: vi.fn(),
}))

vi.mock('@/lib/location-normalization', () => ({
  normalizeLocation: vi.fn(),
}))

vi.mock('@/lib/cache', () => ({
  setCache: vi.fn(),
  getCache: vi.fn(),
  invalidateCache: vi.fn(),
}))

const mockQuery = (await import('@/lib/local-db')).query as unknown as ReturnType<typeof vi.fn>
const mockNormalizeLocation = (await import('@/lib/location-normalization')).normalizeLocation as unknown as ReturnType<typeof vi.fn>

describe('Database Location Functions', () => {
  beforeEach(() => {
    mockQuery.mockReset()
    mockNormalizeLocation.mockReset()
  })

  describe('getCompanyLocations', () => {
    it('should fetch company locations ordered correctly', async () => {
      const mockLocations = [
        {
          id: '1',
          company_id: 'company-1',
          location_raw: 'Berlin',
          location_normalized: 'Berlin, Germany',
          city: 'Berlin',
          country: 'Germany',
          country_code: 'DE',
          is_primary: true,
          is_headquarters: true,
          location_type: 'headquarters'
        },
        {
          id: '2',
          company_id: 'company-1',
          location_raw: 'Munich',
          location_normalized: 'Munich, Germany',
          city: 'Munich',
          country: 'Germany',
          country_code: 'DE',
          is_primary: false,
          is_headquarters: false,
          location_type: 'office'
        }
      ]

      mockQuery.mockResolvedValue({ rows: mockLocations })

      const result = await getCompanyLocations('company-1')

      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM company_locations'),
        ['company-1']
      )
      expect(result).toEqual(mockLocations)
    })

    it('should return empty array for company with no locations', async () => {
      mockQuery.mockResolvedValue({ rows: [] })

      const result = await getCompanyLocations('company-1')

      expect(result).toEqual([])
    })
  })

  describe('addCompanyLocation', () => {
    it('should add a new company location', async () => {
      const mockNormalizedLocation = {
        raw: 'Berlin',
        normalized: 'Berlin, Germany',
        city: 'Berlin',
        country: 'Germany',
        countryCode: 'DE'
      }

      const mockInsertResult = {
        id: 'location-1',
        company_id: 'company-1',
        location_raw: 'Berlin',
        location_normalized: 'Berlin, Germany',
        city: 'Berlin',
        country: 'Germany',
        country_code: 'DE',
        is_primary: true,
        is_headquarters: true,
        location_type: 'headquarters'
      }

      mockNormalizeLocation.mockResolvedValue(mockNormalizedLocation)
      mockQuery.mockResolvedValue({ rows: [mockInsertResult] })

      const result = await addCompanyLocation('company-1', 'Berlin', 'headquarters', true, true)

      expect(mockNormalizeLocation).toHaveBeenCalledWith('Berlin')
      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO company_locations'),
        [
          'company-1',
          'Berlin',
          'Berlin, Germany',
          'Berlin',
          'Germany',
          'DE',
          null,
          null,
          true,
          true,
          'headquarters'
        ]
      )
      expect(result).toEqual(mockInsertResult)
    })

    it('should unset other primary locations when setting as primary', async () => {
      const mockNormalizedLocation = {
        raw: 'Munich',
        normalized: 'Munich, Germany',
        city: 'Munich',
        country: 'Germany',
        countryCode: 'DE'
      }

      mockNormalizeLocation.mockResolvedValue(mockNormalizedLocation)
      mockQuery.mockResolvedValue({ rows: [{}] })

      await addCompanyLocation('company-1', 'Munich', 'office', true, false)

      // Should call UPDATE to unset other primary locations
      expect(mockQuery).toHaveBeenCalledWith(
        'UPDATE company_locations SET is_primary = false WHERE company_id = $1',
        ['company-1']
      )
    })

    it('should unset other headquarters when setting as headquarters', async () => {
      const mockNormalizedLocation = {
        raw: 'Munich',
        normalized: 'Munich, Germany',
        city: 'Munich',
        country: 'Germany',
        countryCode: 'DE'
      }

      mockNormalizeLocation.mockResolvedValue(mockNormalizedLocation)
      mockQuery.mockResolvedValue({ rows: [{}] })

      await addCompanyLocation('company-1', 'Munich', 'headquarters', false, true)

      // Should call UPDATE to unset other headquarters
      expect(mockQuery).toHaveBeenCalledWith(
        'UPDATE company_locations SET is_headquarters = false WHERE company_id = $1',
        ['company-1']
      )
    })
  })

  describe('updateCompanyLocation', () => {
    it('should update location with new data', async () => {
      const mockCurrentLocation = { company_id: 'company-1' }
      const mockNormalizedLocation = {
        raw: 'Updated Location',
        normalized: 'Updated Location, Germany',
        city: 'Updated Location',
        country: 'Germany',
        countryCode: 'DE'
      }

      mockQuery
        .mockResolvedValueOnce({ rows: [mockCurrentLocation] }) // Get current location
        .mockResolvedValueOnce({ rows: [] }) // Update primary (if needed)
        .mockResolvedValueOnce({ rows: [] }) // Update headquarters (if needed)
        .mockResolvedValueOnce({ rows: [{}] }) // Final update

      mockNormalizeLocation.mockResolvedValue(mockNormalizedLocation)

      const updates = {
        location_raw: 'Updated Location',
        is_primary: true
      }

      await updateCompanyLocation('location-1', updates)

      expect(mockQuery).toHaveBeenCalledWith(
        'SELECT company_id FROM company_locations WHERE id = $1',
        ['location-1']
      )
      expect(mockNormalizeLocation).toHaveBeenCalledWith('Updated Location')
    })

    it('should throw error for non-existent location', async () => {
      // Ensure first query returns no rows to trigger error
      mockQuery.mockResolvedValueOnce({ rows: [] })

      mockQuery.mockResolvedValue({ rows: [] })

      await expect(updateCompanyLocation('non-existent', {})).rejects.toThrow('Location not found')
    })
  })

  describe('removeCompanyLocation', () => {
    it('should remove company location', async () => {
      const mockLocation = { company_id: 'company-1' }

      mockQuery
        .mockResolvedValueOnce({ rows: [mockLocation] }) // Get location
        .mockResolvedValueOnce({ rows: [] }) // Delete location

      await removeCompanyLocation('location-1')

      expect(mockQuery).toHaveBeenCalledWith(
        'SELECT company_id FROM company_locations WHERE id = $1',
        ['location-1']
      )
      expect(mockQuery).toHaveBeenCalledWith(
        'DELETE FROM company_locations WHERE id = $1',
        ['location-1']
      )
    })

    it('should throw error for non-existent location', async () => {
      mockQuery.mockResolvedValue({ rows: [] })

      await expect(removeCompanyLocation('non-existent')).rejects.toThrow('Location not found')
    })
  })
})
