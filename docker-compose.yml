version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: benefitlens-postgres
    environment:
      POSTGRES_DB: benefitlens
      POSTGRES_USER: benefitlens_user
      POSTGRES_PASSWORD: benefitlens_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U benefitlens_user -d benefitlens"]
      interval: 10s
      timeout: 5s
      retries: 5



  # Adminer for database management (optional)
  adminer:
    image: adminer:latest
    container_name: benefitlens-adminer
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    environment:
      ADMINER_DEFAULT_SERVER: postgres

  # MailHog for email testing (optional)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: benefitlens-mailhog
    ports:
      - "1025:1025"  # SMTP server
      - "8025:8025"  # Web UI
    healthcheck:
      test: ["<PERSON><PERSON>", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8025"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:

networks:
  default:
    name: benefitlens-network
